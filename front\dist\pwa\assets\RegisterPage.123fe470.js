import{k as x,a0 as y,r as a,I as V,J as w,L as s,M as e,a2 as D,a3 as b,P as Q,a4 as d,A as h,a6 as C,a8 as F,a9 as k}from"./index.3dd97541.js";import{Q as q}from"./QForm.c80c2d0f.js";import{Q as E}from"./QPage.8036a276.js";import{u as R}from"./dialog.d8cf3b2f.js";import{h as P}from"./error-handler.a345d664.js";const _=p=>/^09\d{8}$/.test(p),S=x({__name:"RegisterPage",setup(p){const c=y(),f=a(null),r=a(""),o=a(""),i=a(""),n=a(""),B=a(""),v=a(""),m=a(!1),g=()=>{var t;r.value="",o.value="",i.value="",n.value="",B.value="",v.value="",(t=f.value)==null||t.reset()},A=async()=>{m.value=!0;try{await k.register({uid:r.value,pwd:o.value,name:n.value,phone:B.value,email:v.value}),g(),R().showMessage({title:"\u8A3B\u518A\u6210\u529F",message:"\u5373\u5C07\u524D\u5F80\u767B\u5165\u9801\u9762",timeout:1500,color:"positive",onRedirect:()=>{c.push("/login")}})}catch(t){P(t)}finally{m.value=!1}};return(t,l)=>(V(),w(E,null,{default:s(()=>[e(q,{ref_key:"formRef",ref:f,onSubmit:A,class:"q-py-lg"},{default:s(()=>[e(D,{class:"q-mx-auto q-py-lg q-px-md",style:{"max-width":"min(100%, 28rem)"}},{default:s(()=>[e(b,{class:"q-gutter-md"},{default:s(()=>[l[4]||(l[4]=Q("div",{class:"text-h6"},"\u4F7F\u7528\u8005\u8A3B\u518A",-1)),e(d,{type:"text",modelValue:r.value,"onUpdate:modelValue":l[0]||(l[0]=u=>r.value=u),label:"\u624B\u6A5F\u865F\u78BC(\u5E33\u865F)","stack-label":"",placeholder:"09xxxxxxxx",mask:"##########","unmasked-value":"",outlined:"","lazy-rules":"",rules:[u=>!!u||"\u8ACB\u8F38\u5165\u624B\u6A5F\u865F\u78BC",u=>h(_)(u)||"\u624B\u6A5F\u865F\u78BC\u683C\u5F0F\u932F\u8AA4"]},null,8,["modelValue","rules"]),e(d,{type:"password",modelValue:o.value,"onUpdate:modelValue":l[1]||(l[1]=u=>o.value=u),label:"\u5BC6\u78BC",outlined:"","lazy-rules":"",rules:[u=>!!u||"\u8ACB\u8F38\u5165\u5BC6\u78BC",u=>u.length>=4||"\u5BC6\u78BC\u9577\u5EA6\u9700\u5927\u65BC 4"]},null,8,["modelValue","rules"]),e(d,{type:"password",modelValue:i.value,"onUpdate:modelValue":l[2]||(l[2]=u=>i.value=u),label:"\u78BA\u8A8D\u5BC6\u78BC",outlined:"","lazy-rules":"",rules:[u=>u===o.value||"\u5BC6\u78BC\u4E0D\u4E00\u81F4"]},null,8,["modelValue","rules"]),e(d,{type:"text",modelValue:n.value,"onUpdate:modelValue":l[3]||(l[3]=u=>n.value=u),label:"\u59D3\u540D",outlined:"",rules:[u=>!!u||"\u8ACB\u8F38\u5165\u59D3\u540D"]},null,8,["modelValue","rules"])]),_:1}),e(C,null,{default:s(()=>[e(F,{rounded:"",type:"submit",loading:m.value,color:"login",label:"\u8A3B\u518A",class:"full-width q-mt-md",size:"lg",ripple:{center:!0}},null,8,["loading"])]),_:1}),e(C,null,{default:s(()=>[e(F,{dense:"",flat:"",to:"/login",label:"\u5DF2\u6709\u5E33\u865F\uFF0C\u8FD4\u56DE\u767B\u5165",class:"full-width q-mt-md",size:"md","text-color":"primary",ripple:{center:!0},style:{"font-weight":"bold"}})]),_:1})]),_:1})]),_:1},512)]),_:1}))}});export{S as default};
