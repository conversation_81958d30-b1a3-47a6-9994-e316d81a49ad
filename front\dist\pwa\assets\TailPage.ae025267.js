import{k as fe,r as _,c as ke,b as Ce,w as oe,I as l,J as x,L as n,a2 as Z,M as i,a3 as X,P as t,O as r,az as a,aC as g,aA as b,A as m,aB as V,bm as De,N as y,be as C,a8 as ie,b6 as Fe,bf as Ee,a5 as qe,aD as xe,a6 as Be}from"./index.3dd97541.js";import{Q as K,b as ne}from"./QSelect.1285d7d3.js";import{Q as Ae,a as Pe}from"./QItem.87fe5c76.js";import{Q as Ve}from"./QSpinnerDots.e03dba06.js";import{Q as $e}from"./QLinearProgress.fa832131.js";import{Q as Se}from"./QPage.8036a276.js";import{Q as Ne,a as ve,b as Re,c as be,u as Le}from"./QTabPanels.7809bfe9.js";import{u as he,_ as Qe}from"./IndexPage.cdd7610e.js";import{L as _e}from"./lotto.42d4db2a.js";import{u as ze}from"./useLotteryAnalysis.c47ac7e8.js";import{p as w}from"./padding.dd505b59.js";import{Q as ye}from"./QPagination.08d84e70.js";import{Q as Te,a as we,b as I,c as Me}from"./QTable.4ae3294d.js";import{u as Ue}from"./use-quasar.0e42f40d.js";import{Q as Ie}from"./QSpace.c6b54235.js";import{_ as He}from"./plugin-vue_export-helper.21dcd24c.js";import"./position-engine.bb1fccc0.js";import"./selection.ebf7bbb8.js";import"./QResizeObserver.f0438521.js";import"./touch.9135741d.js";import"./use-render-cache.3aae9b27.js";import"./QPopupProxy.33948bdc.js";import"./QList.e8f9ac34.js";const Oe={class:"row q-gutter-y-md"},je={class:"col-12 col-sm-4 draw-title text-center"},Ge={class:"text-period"},We={class:"text-draw-date"},Je={class:"col-12 col-sm-6 self-center"},Ze={class:"row justify-center"},Ke={key:0,class:"col-auto"},Xe={class:"row q-my-md q-gutter-sm items-center"},Ye={class:"col-sm-auto"},et={class:"col-sm-auto"},tt={class:"col-sm-auto"},lt={class:"row q-my-md"},st={class:"col-auto text-h6"},ut={class:"row q-col-gutter-sm"},at={class:"row items-center"},ot={class:"ball tail-number"},rt={key:0,class:"row q-my-md justify-center"},it={class:"row q-col-gutter-md"},nt={key:0,class:"col-12 q-my-sm"},dt={class:"row q-col-gutter-md items-center text-h6"},ct={class:"col-12 col-sm-4"},mt={class:"col-12 col-sm-6"},pt={class:"row q-col-gutter-md"},vt={class:"col-auto"},bt={class:"col-12 col-sm-2 text-center"},ft={key:1,class:"row justify-center"},gt={class:"row q-my-md"},_t={class:"row q-my-sm"},yt={class:"col text-h6 text-bold"},wt={class:"row q-gutter-xs"},kt={class:"row items-center"},ht={class:"text-h6"},Ct={class:"row justify-center"},Dt={key:0,class:"col-auto"},Ft={class:"row items-center"},Et={class:"col-12 q-mb-sm text-h6 text-center"},qt={class:"col-12 col"},xt={class:"row justify-center"},Bt={key:0,class:"col-auto"},At={class:"col-12 q-mt-md"},Pt={class:"row q-gutter-xs justify-center"},Vt={class:"text-subtitle1",style:{"border-bottom":"1px solid black"}},$t=fe({__name:"TailFollowResult",props:{isSuperLotto:{type:Boolean},drawResults:{},predictResult:{},drawTailResults:{},rdResults:{},occurrenceResults:{},pageSize:{}},emits:["view-detail"],setup(ce){const k=ce,A=!Ue().platform.is.desktop,z=_("1"),$=_(1),E=_({pageItems:[],targetNumAppearances:new Map,totalCount:0,totalPages:0}),h=_(1),de=[{label:"\u4E00\u661F\u7D44\u5408",value:1},{label:"\u4E8C\u661F\u7D44\u5408",value:2},{label:"\u4E09\u661F\u7D44\u5408",value:3},{label:"\u56DB\u661F\u7D44\u5408",value:4},{label:"\u4E94\u661F\u7D44\u5408",value:5}],Y=_(new Map),d=_(1),f=_(1),P=ke(()=>Array.from({length:f.value},(o,u)=>({label:`\u5DF2\u9023\u7E8C\u62D6\u51FA ${u+1} \u6B21`,value:u+1}))),T=_("count"),S=_("above"),G=_([{label:"(\u542B)\u4EE5\u4E0A",value:"above"},{label:"(\u542B)\u4EE5\u4E0B",value:"below"},{label:"\u525B\u597D",value:"exact"}]),M=_([{label:"\u6E96\u78BA\u6B21\u6578\u7D71\u8A08",value:"count"},{label:"\u9810\u6E2C\u7D44\u6578\u7D71\u8A08",value:"group"}]),ee=[{name:"period",label:"\u671F\u6578",field:"period",align:"center"},{name:"draw_number_size",label:"\u958B\u734E\u865F\u78BC",field:"draw_number_size",align:"center",format:o=>o.join(" ")},{name:"tail1",label:"\u5C3E1",field:"tail1",align:"center"},{name:"tail2",label:"\u5C3E2",field:"tail2",align:"center"},{name:"tail3",label:"\u5C3E3",field:"tail3",align:"center"},{name:"tail4",label:"\u5C3E4",field:"tail4",align:"center"},{name:"tail5",label:"\u5C3E5",field:"tail5",align:"center"},{name:"tail6",label:"\u5C3E6",field:"tail6",align:"center"},{name:"tail7",label:"\u5C3E7",field:"tail7",align:"center"},{name:"tail8",label:"\u5C3E8",field:"tail8",align:"center"},{name:"tail9",label:"\u5C3E9",field:"tail9",align:"center"},{name:"tail0",label:"\u5C3E0",field:"tail0",align:"center"}],N=_(!1);Ce(()=>{N.value=!1,O(),R(),N.value=!0}),oe(()=>d.value,()=>{!N.value||R()}),oe(()=>S.value,()=>{!N.value||R()}),oe(()=>T.value,()=>{!N.value||R()}),oe(()=>$.value,()=>{!N.value||te()}),oe(()=>h.value,()=>{p()});const p=()=>{const o=new Map;for(const u of k.drawTailResults){const e=W(u.numbers,h.value),s=Array.from(e);for(const L of s){let Q="";for(let c=0;c<L.length;c++)Q+=`${L[c]}`,c<L.length-1&&(Q+=", ");const F=o.get(Q);F?o.set(Q,F+1):o.set(Q,1)}const v=Array.from(o.entries()).sort((L,Q)=>Q[1]-L[1]);Y.value=new Map(v)}},D=o=>{const u=W(k.predictResult.tailSet||[],h.value),e=Array.from(u),s=[];for(const v of e){let L="";for(let Q=0;Q<v.length;Q++)L+=`${v[Q]}`,Q<v.length-1&&(L+=", ");s.push(L)}return s.includes(o)};function W(o,u){return H(o,u)}function*H(o,u,e=0,s=[]){if(s.length===u){yield[...s];return}for(let v=e;v<o.length;v++)s.push(o[v]),yield*H(o,u,v+1,s),s.pop()}const O=()=>{var o;$.value=1,h.value=1,p();for(let u of k.drawResults){u.tails=new Map;for(let s=0;s<10;s++)u.tails.set(s,[]);let e=[...u.draw_number_size];!k.isSuperLotto&&u.special_number&&(e.push(u.special_number),e=e.sort((s,v)=>s-v));for(const s of e){const v=s%10;(o=u.tails.get(v))==null||o.push(s)}}},U=_([]),R=()=>{U.value=[],$.value=1;const o=new Map;f.value=1;for(const e of k.rdResults){let s=!1;switch(S.value){case"above":s=e.consecutiveHits>=d.value;break;case"below":s=e.consecutiveHits<=d.value;break;case"exact":s=e.consecutiveHits===d.value;break;default:s=e.consecutiveHits>=d.value}if(s){if(U.value.push(e),e.consecutiveHits>f.value&&(f.value=e.consecutiveHits),T.value==="count")for(const v of e.targetNumbers){const L=e.consecutiveHits;o.set(v,(o.get(v)||0)+L)}else if(T.value==="group")for(const v of e.targetNumbers)o.set(v,(o.get(v)||0)+1)}}const u=Array.from(o.entries()).sort((e,s)=>s[1]-e[1]);E.value.targetNumAppearances=new Map(u),te()},te=()=>{const o=($.value-1)*k.pageSize,u=o+k.pageSize;E.value.pageItems=U.value.slice(o,u),E.value.totalCount=U.value.length,E.value.totalPages=Math.ceil(U.value.length/k.pageSize)};return(o,u)=>(l(),x(Z,{class:"q-mt-md"},{default:n(()=>[i(X,null,{default:n(()=>[i(Ne,{modelValue:z.value,"onUpdate:modelValue":u[0]||(u[0]=e=>z.value=e),dense:"",align:"justify",class:"text-h6","active-color":"primary","indicator-color":"primary"},{default:n(()=>[i(ve,{name:"1",label:"\u5206\u6790\u7D50\u679C"}),i(ve,{name:"2",label:"\u7D44\u5408\u7D71\u8A08\u7D50\u679C"}),i(ve,{name:"3",label:"\u958B\u734E\u7D50\u679C"})]),_:1},8,["modelValue"]),o.predictResult.period?(l(),x(Z,{key:0,bordered:"",class:"ball-card full-width q-my-lg"},{default:n(()=>[i(X,null,{default:n(()=>[t("div",Oe,[t("div",je,[u[8]||(u[8]=t("div",{class:"text-h6"},"\u9810\u6E2C\u958B\u734E\u7D50\u679C",-1)),t("div",Ge,"\u7B2C "+r(o.predictResult.period)+" \u671F",1),t("div",We," \u958B\u734E\u65E5\u671F\uFF1A"+r(o.predictResult.draw_date),1)]),t("div",Je,[t("div",Ze,[(l(!0),a(b,null,g(o.predictResult.draw_number_size,e=>(l(),a("div",{key:e,class:"col-auto"},[(l(),a("div",{class:"ball",key:e},r(m(w)(e)),1))]))),128)),o.predictResult.special_number?(l(),a("div",Ke,[(l(),a("div",{class:"ball special-number",key:o.predictResult.special_number},r(m(w)(o.predictResult.special_number)),1))])):V("",!0)])])])]),_:1})]),_:1})):V("",!0),i(Re,{modelValue:z.value,"onUpdate:modelValue":u[7]||(u[7]=e=>z.value=e)},{default:n(()=>[i(be,{name:"1"},{default:n(()=>[t("div",Xe,[u[9]||(u[9]=t("div",{class:"col-sm-auto text-h6"},"\u7BE9\u9078",-1)),t("div",Ye,[i(K,{outlined:"",dense:"",modelValue:d.value,"onUpdate:modelValue":u[1]||(u[1]=e=>d.value=e),options:P.value,"map-options":"","emit-value":""},null,8,["modelValue","options"])]),t("div",et,[i(K,{outlined:"",dense:"",modelValue:S.value,"onUpdate:modelValue":u[2]||(u[2]=e=>S.value=e),options:G.value,"map-options":"","emit-value":""},null,8,["modelValue","options"])]),t("div",tt,[i(K,{outlined:"",dense:"",modelValue:T.value,"onUpdate:modelValue":u[3]||(u[3]=e=>T.value=e),options:M.value,"map-options":"","emit-value":""},null,8,["modelValue","options"])])]),t("div",lt,[t("div",st," \u5171 "+r(E.value.totalCount)+" \u7B46\u8CC7\u6599 ",1)]),u[12]||(u[12]=t("div",{class:"row q-my-sm"},[t("label",{class:"col text-h6 text-bold"},"\u9810\u6E2C\u7D50\u679C")],-1)),i(Z,{flat:"",bordered:"",class:"q-pa-sm q-mb-md text-subtitle1 appearance"},{default:n(()=>[t("div",ut,[(l(!0),a(b,null,g(E.value.targetNumAppearances.keys(),e=>{var s;return l(),a("div",{class:C(["col-4 col-md-2",{predict:(s=o.predictResult.tailSet)==null?void 0:s.includes(e)}]),key:e},[t("div",at,[t("span",ot,r(e),1),y(" ("+r(E.value.targetNumAppearances.get(e))+"\u6B21) ",1)])],2)}),128))])]),_:1}),E.value.totalPages>1?(l(),a("div",rt,[i(ye,{modelValue:$.value,"onUpdate:modelValue":u[4]||(u[4]=e=>$.value=e),max:E.value.totalPages,input:!0},null,8,["modelValue","max"])])):V("",!0),t("div",it,[E.value.pageItems.length===0?(l(),a("div",nt,[i(Z,{flat:"",bordered:""},{default:n(()=>[i(X,null,{default:n(()=>u[10]||(u[10]=[t("div",{class:"text-h6 text-center"},"\u6C92\u6709\u7B26\u5408\u689D\u4EF6\u7684\u7D50\u679C",-1)])),_:1})]),_:1})])):V("",!0),(l(!0),a(b,null,g(E.value.pageItems,(e,s)=>(l(),a("div",{key:s,class:"col-12 q-my-sm"},[i(Z,{flat:"",bordered:""},{default:n(()=>[i(X,null,{default:n(()=>[t("div",dt,[t("div",ct,[t("div",null,[u[11]||(u[11]=y(" \u958B\u51FA ")),(l(!0),a(b,null,g(e.firstNumbers,v=>(l(),x(ne,{key:v,color:"primary","text-color":"white",size:"lg",dense:""},{default:n(()=>[y(r(v)+"\u5C3E ",1)]),_:2},1024))),128)),y(" \u4E0B"+r(e.gap)+"\u671F\u958B ",1),(l(!0),a(b,null,g(e.secondNumbers,v=>(l(),x(ne,{key:v,color:"secondary","text-color":"white",size:"lg",dense:""},{default:n(()=>[y(r(v)+"\u5C3E ",1)]),_:2},1024))),128)),y(" \u518D\u4E0B"+r(e.targetGap)+"\u671F\u9810\u6E2C\u62D6\u51FA ",1),(l(!0),a(b,null,g(e.targetNumbers,v=>(l(),x(ne,{key:v,color:"accent","text-color":"white",size:"lg",dense:""},{default:n(()=>[y(r(v)+"\u5C3E ",1)]),_:2},1024))),128))])]),t("div",mt,[t("div",pt,[t("div",vt," \u5DF2\u9023\u7E8C\u62D6\u51FA"+r(e.consecutiveHits)+"\u6B21 ",1)])]),t("div",bt,[i(ie,{dense:"",color:"primary",icon:"visibility",label:"\u6AA2\u8996\u8A73\u60C5",class:"text-subtitle1 text-bold",onClick:v=>o.$emit("view-detail",e)},null,8,["onClick"])])])]),_:2},1024)]),_:2},1024)]))),128))]),E.value.totalPages>1?(l(),a("div",ft,[i(ye,{modelValue:$.value,"onUpdate:modelValue":u[5]||(u[5]=e=>$.value=e),max:E.value.totalPages,input:!0},null,8,["modelValue","max"])])):V("",!0)]),_:1}),i(be,{name:"2"},{default:n(()=>[t("div",gt,[u[13]||(u[13]=t("span",{class:"text-h6 q-mr-sm"}," \u5C3E\u6578\u7D44\u5408\uFF1A ",-1)),i(K,{modelValue:h.value,"onUpdate:modelValue":u[6]||(u[6]=e=>h.value=e),options:de,"map-options":"","emit-value":"",outlined:"",dense:""},null,8,["modelValue"])]),t("div",_t,[t("label",yt," \u5C3E\u6578\u7D44\u5408\u51FA\u73FE\u6B21\u6578 ("+r(o.drawResults.length)+"\u671F) ",1)]),i(Z,{flat:"",bordered:"",class:"q-pa-sm q-mb-md text-subtitle1 appearance"},{default:n(()=>[t("div",wt,[(l(!0),a(b,null,g(Y.value.keys(),e=>(l(),a("div",{class:C(["col-4 col-md-2",{predict:D(e)}]),key:e},[t("div",kt,[(l(!0),a(b,null,g(e.split(","),s=>(l(),a("span",{class:"ball tail-number",key:s},r(s),1))),128)),y(" ("+r(Y.value.get(e))+"\u6B21) ",1)])],2))),128))])]),_:1})]),_:1}),i(be,{name:"3"},{default:n(()=>[i(Te,{rows:o.drawResults,columns:ee,"rows-per-page-options":[0],"hide-bottom":"",separator:"cell",class:"q-mt-lg"},De({header:n(e=>[A?V("",!0):(l(),x(we,{key:0,props:e,class:"bg-primary text-white"},{default:n(()=>[(l(!0),a(b,null,g(e.cols,s=>(l(),x(Me,{key:s.name,props:e},{default:n(()=>[y(r(s.label),1)]),_:2},1032,["props"]))),128))]),_:2},1032,["props"]))]),_:2},[A?{name:"body",fn:n(e=>[(l(),x(Z,{square:"",bordered:"",key:e.row.period},{default:n(()=>[i(X,{class:"q-px-none q-py-sm"},{default:n(()=>[t("div",Ft,[t("div",Et,[t("span",null,r(e.row.period)+"\u671F ",1),u[25]||(u[25]=t("span",null," | ",-1)),t("span",null,r(e.row.drawDate),1)]),t("div",qt,[t("div",xt,[(l(!0),a(b,null,g(e.row.draw_number_size,s=>(l(),a("div",{key:s,class:"col-auto"},[(l(),a("div",{class:"ball",key:s},r(m(w)(s)),1))]))),128)),e.row.special_number?(l(),a("div",Bt,[(l(),a("div",{class:"ball special-number",key:e.row.special_number},r(m(w)(e.row.special_number)),1))])):V("",!0)])]),t("div",At,[t("div",Pt,[(l(),a(b,null,g([1,2,3,4,5,6,7,8,9,0],s=>t("div",{class:"col-1 text-center",style:{border:"1px solid black"},key:s},[t("div",Vt," \u5C3E"+r(s),1),t("div",null,[(l(!0),a(b,null,g(e.row.tails.get(s),v=>(l(),a("span",{key:v,class:C(["text-h6",{"text-negative":v===e.row.special_number&&!o.isSuperLotto}])},[y(r(m(w)(v)),1),u[26]||(u[26]=t("br",null,null,-1))],2))),128))])])),64))])])])]),_:2},1024)]),_:2},1024))]),key:"1"}:{name:"body",fn:n(e=>[i(we,{props:e},{default:n(()=>[i(I,{key:"period",props:e},{default:n(()=>[t("div",ht,[y(r(e.row.period)+" ",1),u[14]||(u[14]=t("br",null,null,-1)),y(" "+r(e.row.draw_date),1)])]),_:2},1032,["props"]),i(I,{key:"draw_number_size",props:e},{default:n(()=>[t("div",Ct,[(l(!0),a(b,null,g(e.row.draw_number_size,s=>(l(),a("div",{key:s,class:"col-auto"},[(l(),a("div",{class:"ball",key:s},r(m(w)(s)),1))]))),128)),e.row.special_number?(l(),a("div",Dt,[(l(),a("div",{class:"ball special-number",key:e.row.special_number},r(m(w)(e.row.special_number)),1))])):V("",!0)])]),_:2},1032,["props"]),i(I,{key:"tail1",props:e},{default:n(()=>[(l(!0),a(b,null,g(e.row.tails.get(1),s=>(l(),a("span",{key:s,class:C(["text-h6",{"text-negative":s===e.row.special_number&&!o.isSuperLotto}])},[y(r(m(w)(s))+" ",1),u[15]||(u[15]=t("br",null,null,-1))],2))),128))]),_:2},1032,["props"]),i(I,{key:"tail2",props:e},{default:n(()=>[(l(!0),a(b,null,g(e.row.tails.get(2),s=>(l(),a("span",{key:s,class:C(["text-h6",{"text-negative":s===e.row.special_number&&!o.isSuperLotto}])},[y(r(m(w)(s)),1),u[16]||(u[16]=t("br",null,null,-1))],2))),128))]),_:2},1032,["props"]),i(I,{key:"tail3",props:e},{default:n(()=>[(l(!0),a(b,null,g(e.row.tails.get(3),s=>(l(),a("span",{key:s,class:C(["text-h6",{"text-negative":s===e.row.special_number&&!o.isSuperLotto}])},[y(r(m(w)(s)),1),u[17]||(u[17]=t("br",null,null,-1))],2))),128))]),_:2},1032,["props"]),i(I,{key:"tail4",props:e},{default:n(()=>[(l(!0),a(b,null,g(e.row.tails.get(4),s=>(l(),a("span",{key:s,class:C(["text-h6",{"text-negative":s===e.row.special_number&&!o.isSuperLotto}])},[y(r(m(w)(s)),1),u[18]||(u[18]=t("br",null,null,-1))],2))),128))]),_:2},1032,["props"]),i(I,{key:"tail5",props:e},{default:n(()=>[(l(!0),a(b,null,g(e.row.tails.get(5),s=>(l(),a("span",{key:s,class:C(["text-h6",{"text-negative":s===e.row.special_number&&!o.isSuperLotto}])},[y(r(m(w)(s)),1),u[19]||(u[19]=t("br",null,null,-1))],2))),128))]),_:2},1032,["props"]),i(I,{key:"tail6",props:e},{default:n(()=>[(l(!0),a(b,null,g(e.row.tails.get(6),s=>(l(),a("span",{key:s,class:C(["text-h6",{"text-negative":s===e.row.special_number&&!o.isSuperLotto}])},[y(r(m(w)(s)),1),u[20]||(u[20]=t("br",null,null,-1))],2))),128))]),_:2},1032,["props"]),i(I,{key:"tail7",props:e},{default:n(()=>[(l(!0),a(b,null,g(e.row.tails.get(7),s=>(l(),a("span",{key:s,class:C(["text-h6",{"text-negative":s===e.row.special_number&&!o.isSuperLotto}])},[y(r(m(w)(s)),1),u[21]||(u[21]=t("br",null,null,-1))],2))),128))]),_:2},1032,["props"]),i(I,{key:"tail8",props:e},{default:n(()=>[(l(!0),a(b,null,g(e.row.tails.get(8),s=>(l(),a("span",{key:s,class:C(["text-h6",{"text-negative":s===e.row.special_number&&!o.isSuperLotto}])},[y(r(m(w)(s)),1),u[22]||(u[22]=t("br",null,null,-1))],2))),128))]),_:2},1032,["props"]),i(I,{key:"tail9",props:e},{default:n(()=>[(l(!0),a(b,null,g(e.row.tails.get(9),s=>(l(),a("span",{key:s,class:C(["text-h6",{"text-negative":s===e.row.special_number&&!o.isSuperLotto}])},[y(r(m(w)(s)),1),u[23]||(u[23]=t("br",null,null,-1))],2))),128))]),_:2},1032,["props"]),i(I,{key:"tail0",props:e},{default:n(()=>[(l(!0),a(b,null,g(e.row.tails.get(0),s=>(l(),a("span",{key:s,class:C(["text-h6",{"text-negative":s===e.row.special_number&&!o.isSuperLotto}])},[y(r(m(w)(s)),1),u[24]||(u[24]=t("br",null,null,-1))],2))),128))]),_:2},1032,["props"])]),_:2},1032,["props"])]),key:"0"}]),1032,["rows"])]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1}))}});const St={class:"text-h6 q-mb-md"},Nt={class:"text-h6 q-mb-md"},Rt={class:"row text-h6 q-mb-md"},Lt={class:"col-auto"},Qt={class:"row balls"},zt={key:0,class:"col-auto"},Tt={key:0,class:"row text-h6 q-mb-md"},Mt={class:"col-auto"},Ut={class:"row balls"},It={key:0,class:"col-auto"},Ht={key:1,class:"row text-h6"},Ot={class:"col-auto"},jt={class:"row balls"},Gt={key:0,class:"col-auto"},Wt={key:2,class:"predict-section"},Jt={class:"row text-h6 q-mt-sm"},Zt={class:"col-auto"},Kt={key:0,class:"predict-period"},Xt={class:"predict-date"},Yt={class:"row balls"},el={key:0,class:"col-auto"},tl={key:1,class:"col-auto pending-draw"},ll=fe({__name:"TailFollowDetail",props:{modelValue:{type:Boolean},results:{},selectedDetail:{},occurrences:{},predictResult:{}},emits:["update:modelValue"],setup(ce,{emit:k}){const B=ce,A=he(),z=ke({get:()=>B.modelValue,set:d=>$("update:modelValue",d)}),$=k,E=d=>{if(!d)return"";const f=B.results.find(P=>P.period==d);return f==null?void 0:f.draw_date},h=d=>{if(!d)return{numbers:[],specialNumber:0};const f=B.results.find(P=>P.period==d);return{numbers:f==null?void 0:f.draw_number_size,specialNumber:f!=null&&f.special_number?Number(f.special_number):0}},de=d=>{var ee,N,p;if(!d)return[];const f=d.firstNumbers.join(",")+"-"+d.secondNumbers.join(",")+"-"+d.gap+"-"+d.targetGap,P=(N=(ee=B.occurrences.get(f))==null?void 0:ee.periods)!=null?N:[],T=P.filter(D=>D.targetPeriod===void 0||D.targetPeriod===null||D.targetPeriod.trim()===""),S=P.filter(D=>D.targetPeriod!==void 0&&D.targetPeriod!==null&&D.targetPeriod.trim()!==""),G=(p=d.consecutiveHits)!=null?p:0;let M=[];return G>0&&S.length>0&&(M=S.sort((W,H)=>{const O=parseInt(W.targetPeriod),U=parseInt(H.targetPeriod);return isNaN(O)||isNaN(U)?W.targetPeriod.localeCompare(H.targetPeriod):U-O}).slice(0,G)),T.length>0&&(M=[...T,...M]),M},Y=d=>d.targetPeriod===void 0;return(d,f)=>(l(),x(Fe,{modelValue:z.value,"onUpdate:modelValue":f[1]||(f[1]=P=>z.value=P)},{default:n(()=>[i(Z,{style:{"max-width":"100%",width:"800px"}},{default:n(()=>[i(X,{class:"row items-center"},{default:n(()=>[f[2]||(f[2]=t("div",{class:"text-h6"},"\u8A73\u7D30\u8CC7\u6599",-1)),i(Ie),i(ie,{icon:"close",flat:"",round:"",dense:"",onClick:f[0]||(f[0]=P=>z.value=!1)})]),_:1}),i(X,{class:"q-pa-md"},{default:n(()=>{var P,T,S,G,M,ee,N;return[t("div",St,[f[3]||(f[3]=y(" \u958B\u51FA ")),(l(!0),a(b,null,g((P=d.selectedDetail)==null?void 0:P.firstNumbers,p=>(l(),x(ne,{key:p,color:"primary","text-color":"white",class:"text-h6"},{default:n(()=>[y(r(p)+"\u5C3E ",1)]),_:2},1024))),128)),y(" \u4E0B"+r((T=d.selectedDetail)==null?void 0:T.gap)+"\u671F\u958B ",1),(l(!0),a(b,null,g((S=d.selectedDetail)==null?void 0:S.secondNumbers,p=>(l(),x(ne,{key:p,color:"secondary","text-color":"white",class:"text-h6"},{default:n(()=>[y(r(p)+"\u5C3E ",1)]),_:2},1024))),128)),y(" \u518D\u4E0B "+r((G=d.selectedDetail)==null?void 0:G.targetGap)+" \u671F\u9810\u6E2C\u62D6\u51FA ",1),(l(!0),a(b,null,g((M=d.selectedDetail)==null?void 0:M.targetNumbers,p=>(l(),x(ne,{key:p,color:"accent","text-color":"white",class:"text-h6"},{default:n(()=>[y(r(p)+"\u5C3E ",1)]),_:2},1024))),128))]),t("div",Nt," \u5DF2\u9023\u7E8C\u62D6\u51FA"+r((N=(ee=d.selectedDetail)==null?void 0:ee.consecutiveHits)!=null?N:0)+"\u6B21 ",1),t("div",null,[(l(!0),a(b,null,g(de(d.selectedDetail),(p,D)=>(l(),x(Z,{key:D,class:"q-mb-md",style:Ee({"background-color":Y(p)?"#e8f5e8":"#fefefe"})},{default:n(()=>[i(X,null,{default:n(()=>{var W,H,O,U,R,te;return[t("div",Rt,[t("div",Lt,[t("div",null,"\u7B2C "+r(p.firstPeriod)+" \u671F",1),t("div",null," \u958B\u734E\u65E5\u671F\uFF1A"+r(E(p.firstPeriod)),1)]),t("div",Qt,[(l(!0),a(b,null,g(h(p.firstPeriod).numbers,(o,u)=>{var e;return l(),a("div",{class:"col-auto",key:u},[t("div",{class:C(["ball",{"first-num":(e=d.selectedDetail)==null?void 0:e.firstNumbers.includes(o%10)}])},r(m(w)(o)),3)])}),128)),h(p.firstPeriod).specialNumber?(l(),a("div",zt,[t("div",{class:C(["ball special-number",{"first-num":((W=d.selectedDetail)==null?void 0:W.firstNumbers.includes(h(p.firstPeriod).specialNumber%10))&&!m(A).isSuperLotto}])},r(m(w)(h(p.firstPeriod).specialNumber)),3)])):V("",!0)])]),p.secondPeriod?(l(),a("div",Tt,[t("div",Mt,[t("div",null,"\u7B2C "+r(p.secondPeriod)+" \u671F",1),t("div",null," \u958B\u734E\u65E5\u671F\uFF1A"+r((H=E(p.secondPeriod))!=null?H:"\u672A\u958B\u734E"),1)]),t("div",Ut,[(l(!0),a(b,null,g(h(p.secondPeriod).numbers,(o,u)=>{var e;return l(),a("div",{class:"col-auto",key:u},[t("div",{class:C(["ball",{"second-num":(e=d.selectedDetail)==null?void 0:e.secondNumbers.includes(o%10)}])},r(m(w)(o)),3)])}),128)),h(p.secondPeriod).specialNumber?(l(),a("div",It,[t("div",{class:C(["ball special-number",{"second-num":((O=d.selectedDetail)==null?void 0:O.secondNumbers.includes(h(p.secondPeriod).specialNumber%10))&&!m(A).isSuperLotto}])},r(m(w)(h(p.secondPeriod).specialNumber)),3)])):V("",!0)])])):V("",!0),p.targetPeriod?(l(),a("div",Ht,[t("div",Ot,[t("div",null,"\u7B2C "+r(p.targetPeriod)+" \u671F",1),t("div",null," \u958B\u734E\u65E5\u671F\uFF1A"+r((U=E(p.targetPeriod))!=null?U:"\u672A\u958B\u734E"),1)]),t("div",jt,[(l(!0),a(b,null,g(h(p.targetPeriod).numbers,(o,u)=>{var e;return l(),a("div",{class:"col-auto",key:u},[t("div",{class:C(["ball",{"target-num":(e=d.selectedDetail)==null?void 0:e.targetNumbers.includes(o%10)}])},r(m(w)(o)),3)])}),128)),h(p.targetPeriod).specialNumber?(l(),a("div",Gt,[t("div",{class:C(["ball special-number",{"target-num":((R=d.selectedDetail)==null?void 0:R.targetNumbers.includes(h(p.targetPeriod).specialNumber%10))&&!m(A).isSuperLotto}])},r(m(w)(h(p.targetPeriod).specialNumber)),3)])):V("",!0)])])):(l(),a("div",Wt,[t("div",Jt,[t("div",Zt,[d.predictResult.period?(l(),a("div",Kt," \u7B2C "+r(d.predictResult.period)+" \u671F ",1)):V("",!0),t("div",Xt,[d.predictResult.period?(l(),a(b,{key:0},[y(" \u5BE6\u969B\u958B\u734E\u65E5\u671F\uFF1A"+r(d.predictResult.draw_date),1)],64)):(l(),a(b,{key:1},[y(" \u9810\u6E2C\u671F\u865F\uFF1A\u5C1A\u672A\u958B\u734E ")],64))])]),t("div",Yt,[d.predictResult.period?(l(),a(b,{key:0},[(l(!0),a(b,null,g(d.predictResult.draw_number_size,(o,u)=>{var e;return l(),a("div",{class:"col-auto",key:u},[t("div",{class:C(["ball",{"target-num":(e=d.selectedDetail)==null?void 0:e.targetNumbers.includes(o%10)}])},r(m(w)(o)),3)])}),128)),d.predictResult.special_number?(l(),a("div",el,[t("div",{class:C(["ball special-number",{"target-num":((te=d.selectedDetail)==null?void 0:te.targetNumbers.includes(d.predictResult.special_number%10))&&!m(A).isSuperLotto}])},r(m(w)(d.predictResult.special_number)),3)])):V("",!0)],64)):(l(),a("div",tl,[i(qe,{name:"schedule",size:"lg"}),f[4]||(f[4]=t("span",null,"\u5C1A\u672A\u958B\u734E",-1))]))])])]))]}),_:2},1024)]),_:2},1032,["style"]))),128))])]}),_:1})]),_:1})]),_:1},8,["modelValue"]))}});var sl=He(ll,[["__scopeId","data-v-164dfad4"]]);const ul={class:"row lto-ref q-mb-sm"},al={class:"col-12 col-sm-4 self-center text-h6"},ol={class:"col-12 col-sm-6 self-center text-subtitle1"},rl={class:"row balls"},il={class:"ball"},nl={key:0,class:"col-auto"},dl={class:"row q-mb-md"},cl={class:"col"},ml={key:1,class:"row q-mb-md"},pl={class:"row q-mb-md"},vl={class:"col-12 col-sm-4 q-pa-sm"},bl={class:"col-12 col-sm-4 q-pa-sm"},fl={class:"col-12 col-sm-4 q-pa-sm"},gl={class:"row q-mb-md"},_l={class:"col-12 col-sm-4"},yl={class:"q-pa-sm"},wl={class:"col-12 col-sm-4"},kl={class:"q-pa-sm"},hl={class:"col-12 col-sm-4"},Cl={class:"q-pa-sm"},Dl={class:"text-center q-mb-sm"},Wl=fe({__name:"TailPage",setup(ce){const k=Le(),B=he(),A=_(B.getLotto),z=ze(),$=_(!1),E=()=>{$.value=!0},h=()=>{$.value=!1},de=()=>{$.value=!1};oe(()=>B.getLotto,F=>{F&&(A.value=F)}),oe(()=>{var F;return(F=A.value)==null?void 0:F.period},()=>{D.value=[]});const Y=[{label:"\u4E00\u661F\u7D44\u5408",value:1},{label:"\u4E8C\u661F\u7D44\u5408",value:2},{label:"\u4E09\u661F\u7D44\u5408",value:3},{label:"\u56DB\u661F\u7D44\u5408",value:4},{label:"\u4E94\u661F\u7D44\u5408",value:5}],d=_(1),f=_(1),P=_(1),T=Array.from({length:21},(F,c)=>({label:`${c+10}\u671F`,value:c+10})),S=_(20);let G=_(Array.from({length:991},(F,c)=>({label:`${c+10}\u671F`,value:c+10})));const M=_(50),ee=(F,c,le)=>{const se=parseInt(F,10);(se<10||se>1e3)&&le(),c(()=>{G.value=Array.from({length:991},(J,ae)=>ae+10).filter(J=>J.toString().startsWith(F)).map(J=>({label:`${J.toString()}\u671F`,value:J}))})},N=Array.from({length:15},(F,c)=>({label:`\u4E0B${c+1}\u671F`,value:c+1})),p=_(1),D=_([]),W=_("super_lotto638"),H=_([]),O=_(new Map),U=_(new Map),R=_({period:"",draw_date:"",draw_number_appear:[],draw_number_size:[],tails:new Map}),te=_([]),o=_(!1),u=async()=>{var F,c,le;try{W.value=B.drawType,o.value=B.isSuperLotto,k.startCalculating(),e();const se=await _e.getLottoList({draw_type:B.getDrawType,date_end:(c=(F=A.value)==null?void 0:F.draw_date)!=null?c:"",limit:M.value});D.value=se.data;const J=await _e.getLottoPredict({draw_type:B.getDrawType,draw_date:(le=B.getLotto)==null?void 0:le.draw_date,ahead_count:p.value});R.value=J.data,R.value.period&&(R.value.tailSet=z.getTailSet(R.value,o.value)),te.value=D.value.map(j=>{const ue=new Set;for(let q of j.draw_number_size)ue.add(q%10);j.special_number&&!B.isSuperLotto&&ue.add(j.special_number%10);const pe=Array.from(ue).sort((q,ge)=>q===0?1:ge===0?-1:q-ge);return{period:String(j.period),numbers:[...pe]}}).reverse(),z.init({firstGroupSize:d.value,secondGroupSize:f.value,targetGroupSize:P.value,maxRange:S.value,lookAheadCount:p.value},te.value);let ae=Date.now();const me=8,re=await z.analyzeWithProgress(async j=>{const ue=Date.now();ue-ae>=me&&(await k.updateProgress(j),ae=ue)},j=>{k.addWarning(j)});H.value=re.data,O.value=re.occurrences,U.value=re.matchData}catch(se){console.error(se)}finally{s()}},e=()=>{D.value=[]},s=()=>{z.stopAnalyzer(),k.stopCalculating()},v=_(!1),L=_(null),Q=F=>{L.value=F,v.value=!0};return(F,c)=>(l(),x(Se,{class:"justify-center"},{default:n(()=>[i(Z,{class:"q-mx-auto q-py-sm"},{default:n(()=>[i(X,null,{default:n(()=>{var le,se,J,ae,me,re,j,ue,pe;return[(le=m(B).getLotto)!=null&&le.draw_date?(l(),a(b,{key:0},[t("div",ul,[t("div",al,[t("div",null,r(m(B).getDrawLabel),1),c[7]||(c[7]=t("span",null,"\u53C3\u8003\u671F\u865F\uFF1A",-1)),t("span",null,r((se=A.value)==null?void 0:se.period),1),t("span",null,"\uFF08"+r((J=A.value)==null?void 0:J.draw_date)+"\uFF09",1)]),t("div",ol,[t("div",rl,[(l(!0),a(b,null,g((ae=A.value)==null?void 0:ae.draw_number_size,q=>(l(),a("div",{class:"col-auto",key:q},[t("div",il,r(m(w)(q)),1)]))),128)),(me=A.value)!=null&&me.special_number?(l(),a("div",nl,[(l(),a("div",{class:"ball special-number",key:(re=A.value)==null?void 0:re.special_number},r(m(w)((j=A.value)==null?void 0:j.special_number)),1))])):V("",!0)])])]),t("div",dl,[t("div",cl,[$.value?(l(),x(ie,{key:1,type:"button",label:"\u53D6\u6D88\u9078\u64C7",color:"negative",class:"text-h6 q-ml-md",onClick:de})):(l(),x(ie,{key:0,type:"button",label:"\u91CD\u65B0\u9078\u64C7",color:"primary",class:"text-h6 q-ml-md",onClick:E}))])])],64)):(l(),a("div",ml,c[8]||(c[8]=[t("div",{class:"text-h6"},"\u203B\u8ACB\u9078\u64C7\u53C3\u8003\u671F\u865F",-1)]))),i(xe,{class:"q-mb-md"}),!$.value&&((ue=m(B).getLotto)==null?void 0:ue.draw_date)?(l(),a(b,{key:2},[c[14]||(c[14]=t("div",{class:"row q-mb-md"},[t("div",{class:"col-12 text-h5 text-weight-bolder text-center"}," \u5C3E\u6578\u5206\u6790\u8A2D\u5B9A ")],-1)),t("div",pl,[c[9]||(c[9]=t("div",{class:"col-12 text-h6 text-weight-bold"},"\u5C3E\u6578\u62D6\u724C\u7D44\u5408",-1)),t("div",vl,[i(K,{outlined:"",dense:"",modelValue:d.value,"onUpdate:modelValue":c[0]||(c[0]=q=>d.value=q),options:Y,"emit-value":"","map-options":""},null,8,["modelValue"])]),t("div",bl,[i(K,{outlined:"",dense:"",modelValue:f.value,"onUpdate:modelValue":c[1]||(c[1]=q=>f.value=q),options:Y,"emit-value":"","map-options":""},null,8,["modelValue"])]),t("div",fl,[i(K,{outlined:"",dense:"",modelValue:P.value,"onUpdate:modelValue":c[2]||(c[2]=q=>P.value=q),options:Y,"emit-value":"","map-options":""},null,8,["modelValue"])])]),t("div",gl,[t("div",_l,[c[11]||(c[11]=t("div",{class:"text-h6 text-weight-bold"},"\u63A8\u7B97\u671F\u6578",-1)),t("div",yl,[i(K,{outlined:"",dense:"",modelValue:M.value,"onUpdate:modelValue":c[3]||(c[3]=q=>M.value=q),options:m(G),"input-debounce":"0","use-input":"","hide-selected":"","fill-input":"",onFilter:ee,"emit-value":"","map-options":""},{"no-option":n(()=>[i(Ae,null,{default:n(()=>[i(Pe,{class:"text-grey"},{default:n(()=>c[10]||(c[10]=[y(" \u7121\u53EF\u7528\u9078\u9805 ")])),_:1})]),_:1})]),_:1},8,["modelValue","options"])])]),t("div",wl,[c[12]||(c[12]=t("div",{class:"text-h6 text-weight-bold"},"\u6700\u5927\u5340\u9593",-1)),t("div",kl,[i(K,{outlined:"",dense:"",modelValue:S.value,"onUpdate:modelValue":c[4]||(c[4]=q=>S.value=q),options:m(T),"emit-value":"","map-options":""},null,8,["modelValue","options"])])]),t("div",hl,[c[13]||(c[13]=t("div",{class:"text-h6 text-weight-bold"},"\u9810\u6E2C\u671F\u6578",-1)),t("div",Cl,[i(K,{outlined:"",dense:"",modelValue:p.value,"onUpdate:modelValue":c[5]||(c[5]=q=>p.value=q),options:m(N),"emit-value":"","map-options":""},null,8,["modelValue","options"])])])]),i(Be,{align:"right",class:"q-my-lg q-py-none q-px-md"},{default:n(()=>[m(k).isCalculating?(l(),x(ie,{key:0,type:"button",label:"\u4E2D\u65B7\u8A08\u7B97",color:"negative",class:"text-h6 q-mr-md",onClick:s})):V("",!0),i(ie,{type:"button",label:"\u958B\u59CB\u8A08\u7B97",color:"positive",class:"text-h6 q-px-lg q-py-sm",onClick:u,loading:m(k).isCalculating},{loading:n(()=>[i(Ve)]),_:1},8,["loading"])]),_:1})],64)):(l(),x(Qe,{key:3,"draw-type-query":m(B).drawType,"date-query":((pe=A.value)==null?void 0:pe.draw_date)||"",isSelectRef:!0,onSelectRef:h},null,8,["draw-type-query","date-query"]))]}),_:1}),m(k).isCalculating?(l(),x(X,{key:0},{default:n(()=>[t("div",Dl,r(m(k).progressMessage),1),i($e,{rounded:"",size:"md",value:m(k).progress,"animation-speed":50,color:"primary",class:"q-mb-xs"},null,8,["value"])]),_:1})):V("",!0)]),_:1}),!m(k).isCalculating&&D.value.length>0?(l(),a(b,{key:0},[i($t,{"is-super-lotto":o.value,"draw-results":D.value,"predict-result":R.value,"draw-tail-results":te.value,"rd-results":H.value,"occurrence-results":O.value,"page-size":50,onViewDetail:Q},null,8,["is-super-lotto","draw-results","predict-result","draw-tail-results","rd-results","occurrence-results"]),i(sl,{modelValue:v.value,"onUpdate:modelValue":c[6]||(c[6]=le=>v.value=le),results:D.value,"predict-result":R.value,"selected-detail":L.value,occurrences:O.value},null,8,["modelValue","results","predict-result","selected-detail","occurrences"])],64)):V("",!0)]),_:1}))}});export{Wl as default};
