import{k as o,I as s,az as n,P as t,M as l,a8 as r}from"./index.bf55edc3.js";const a={class:"fullscreen bg-blue text-white text-center q-pa-md flex flex-center"},p=o({name:"ErrorNotFound",__name:"ErrorNotFound",setup(d){return(i,e)=>(s(),n("div",a,[t("div",null,[e[0]||(e[0]=t("div",{style:{"font-size":"30vh"}},"404",-1)),e[1]||(e[1]=t("div",{class:"text-h2",style:{opacity:"0.4"}},"Oops. Nothing here...",-1)),l(r,{class:"q-mt-xl",color:"white","text-color":"blue",unelevated:"",to:"/index",label:"Go Home","no-caps":""})])]))}});export{p as default};
