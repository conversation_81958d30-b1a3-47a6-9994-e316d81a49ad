import{k as S,r,b as _,bx as g,g as V,I as q,J as W,L as s,P as e,M as l,a3 as f,N as o,O as d,a6 as U,a8 as F,a2 as y,by as I}from"./index.3dd97541.js";import{Q}from"./QPage.8036a276.js";import{u as T}from"./use-quasar.0e42f40d.js";const z={class:"q-pa-md",style:{"max-width":"600px"}},P={class:"q-mb-md"},L={class:"q-mb-md"},M={class:"q-mb-md"},N={class:"q-mb-md"},R={class:"q-mb-md"},j={class:"q-mb-md"},G=S({__name:"UpdateTestPage",setup(H){const a=T(),x=r(""),c=r({baseVersion:"",buildVersion:"",timestamp:""}),n=r("\u6AA2\u67E5\u4E2D..."),i=r("\u6B63\u5E38"),p=r(""),C=r(!1),A=r(!1);let v=null;_(()=>{x.value=g.getCurrentVersion(),c.value=g.getVersionInfo(),D(),v=window.setInterval(()=>{D(),p.value=new Date().toLocaleTimeString()},5e3)}),V(()=>{v&&clearInterval(v)});const D=async()=>{if("serviceWorker"in navigator)try{const t=await navigator.serviceWorker.getRegistration();t?t.active?n.value="\u5DF2\u555F\u52D5":t.installing?n.value="\u5B89\u88DD\u4E2D":t.waiting?n.value="\u7B49\u5F85\u4E2D":n.value="\u672A\u77E5\u72C0\u614B":n.value="\u672A\u8A3B\u518A"}catch{n.value="\u6AA2\u67E5\u5931\u6557"}else n.value="\u4E0D\u652F\u63F4"},b=async()=>{C.value=!0,i.value="\u6AA2\u67E5\u4E2D...";try{console.log("\u958B\u59CB\u624B\u52D5\u6AA2\u67E5\u66F4\u65B0...");let t=!1;try{const u=await I.manualCheck();console.log("Service Worker \u66F4\u65B0\u6AA2\u67E5\u7D50\u679C:",u),u&&(t=!0,i.value="\u767C\u73FE Service Worker \u66F4\u65B0")}catch(u){console.warn("Service Worker \u66F4\u65B0\u6AA2\u67E5\u5931\u6557:",u)}try{const u=await g.manualVersionCheck();console.log("\u7248\u672C\u66F4\u65B0\u6AA2\u67E5\u7D50\u679C:",u),u&&(t=!0,i.value="\u767C\u73FE\u7248\u672C\u66F4\u65B0")}catch(u){console.warn("\u7248\u672C\u66F4\u65B0\u6AA2\u67E5\u5931\u6557:",u)}t?(i.value="\u767C\u73FE\u66F4\u65B0\uFF0C\u7B49\u5F85\u7528\u6236\u78BA\u8A8D",a.notify({message:"\u767C\u73FE\u65B0\u7248\u672C\uFF0C\u8ACB\u5728\u5F48\u51FA\u7684\u5C0D\u8A71\u6846\u4E2D\u78BA\u8A8D\u662F\u5426\u66F4\u65B0",color:"positive",icon:"cloud_download",timeout:5e3})):(i.value="\u5DF2\u662F\u6700\u65B0\u7248\u672C",a.notify({message:"\u5DF2\u662F\u6700\u65B0\u7248\u672C",color:"info",icon:"check_circle"}))}catch(t){console.error("\u624B\u52D5\u66F4\u65B0\u6AA2\u67E5\u5931\u6557:",t),i.value="\u6AA2\u67E5\u5931\u6557: "+(t instanceof Error?t.message:String(t)),a.notify({message:"\u6AA2\u67E5\u66F4\u65B0\u5931\u6557: "+(t instanceof Error?t.message:String(t)),color:"negative",icon:"error",timeout:5e3})}finally{C.value=!1}},k=async()=>{A.value=!0;try{if("caches"in window){const t=await caches.keys();await Promise.all(t.map(u=>caches.delete(u))),a.notify({message:"\u7DE9\u5B58\u5DF2\u6E05\u9664",color:"positive",icon:"delete_sweep"})}}catch{a.notify({message:"\u6E05\u9664\u7DE9\u5B58\u5931\u6557",color:"negative",icon:"error"})}finally{A.value=!1}},w=()=>{window.location.reload()},h=()=>{(()=>{const u=document.createElement("div");u.style.cssText=`
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 10000;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `;const m=document.createElement("div");m.style.cssText=`
      background: white;
      padding: 24px;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      max-width: 400px;
      text-align: center;
    `,m.innerHTML=`
      <div style="margin-bottom: 16px;">
        <div style="font-size: 18px; font-weight: 600; color: #1976d2; margin-bottom: 8px;">
          \u{1F389} \u767C\u73FE\u65B0\u7248\u672C\uFF01
        </div>
        <div style="color: #666; font-size: 14px;">
          \u61C9\u7528\u7A0B\u5F0F\u6709\u65B0\u7248\u672C\u53EF\u7528\uFF0C\u662F\u5426\u8981\u7ACB\u5373\u66F4\u65B0\uFF1F<br>
          <small style="color: #999;">(\u9019\u662F\u6E2C\u8A66\u5C0D\u8A71\u6846)</small>
        </div>
      </div>
      <div style="display: flex; gap: 12px; justify-content: center;">
        <button id="test-update-later" style="
          padding: 8px 16px;
          border: 1px solid #ddd;
          background: white;
          border-radius: 4px;
          cursor: pointer;
          font-size: 14px;
        ">\u7A0D\u5F8C\u66F4\u65B0</button>
        <button id="test-update-now" style="
          padding: 8px 16px;
          border: none;
          background: #1976d2;
          color: white;
          border-radius: 4px;
          cursor: pointer;
          font-size: 14px;
        ">\u7ACB\u5373\u66F4\u65B0</button>
      </div>
    `,u.appendChild(m),document.body.appendChild(u);const B=u.querySelector("#test-update-now"),E=u.querySelector("#test-update-later");B==null||B.addEventListener("click",()=>{document.body.removeChild(u),a.notify({message:"\u6E2C\u8A66\uFF1A\u7528\u6236\u9078\u64C7\u7ACB\u5373\u66F4\u65B0",color:"positive",icon:"check_circle"})}),E==null||E.addEventListener("click",()=>{document.body.removeChild(u),a.notify({message:"\u6E2C\u8A66\uFF1A\u7528\u6236\u9078\u64C7\u7A0D\u5F8C\u66F4\u65B0",color:"info",icon:"schedule"})})})()};return(t,u)=>(q(),W(Q,{class:"flex flex-center"},{default:s(()=>[e("div",z,[l(y,null,{default:s(()=>[l(f,null,{default:s(()=>u[0]||(u[0]=[e("div",{class:"text-h6"},"\u81EA\u52D5\u66F4\u65B0\u6E2C\u8A66\u9801\u9762",-1),e("div",{class:"text-subtitle2"},"\u6E2C\u8A66 PWA \u81EA\u52D5\u66F4\u65B0\u529F\u80FD",-1)])),_:1}),l(f,null,{default:s(()=>[e("div",P,[u[1]||(u[1]=e("strong",null,"\u7576\u524D\u7248\u672C:",-1)),o(" "+d(c.value.baseVersion),1)]),e("div",L,[u[2]||(u[2]=e("strong",null,"\u69CB\u5EFA\u7248\u672C:",-1)),o(" "+d(c.value.buildVersion),1)]),e("div",M,[u[3]||(u[3]=e("strong",null,"\u69CB\u5EFA\u6642\u9593\u6233:",-1)),o(" "+d(c.value.timestamp),1)]),e("div",N,[u[4]||(u[4]=e("strong",null,"Service Worker \u72C0\u614B:",-1)),o(" "+d(n.value),1)]),e("div",R,[u[5]||(u[5]=e("strong",null,"\u66F4\u65B0\u6AA2\u67E5\u72C0\u614B:",-1)),o(" "+d(i.value),1)]),e("div",j,[u[6]||(u[6]=e("strong",null,"\u6700\u5F8C\u6AA2\u67E5\u6642\u9593:",-1)),o(" "+d(p.value),1)])]),_:1}),l(U,null,{default:s(()=>[l(F,{color:"primary",onClick:b,loading:C.value},{default:s(()=>u[7]||(u[7]=[o(" \u624B\u52D5\u6AA2\u67E5\u66F4\u65B0 ")])),_:1},8,["loading"]),l(F,{color:"secondary",onClick:k,loading:A.value},{default:s(()=>u[8]||(u[8]=[o(" \u6E05\u9664\u7DE9\u5B58 ")])),_:1},8,["loading"]),l(F,{color:"negative",onClick:w},{default:s(()=>u[9]||(u[9]=[o(" \u5F37\u5236\u91CD\u8F09 ")])),_:1}),l(F,{color:"warning",onClick:h},{default:s(()=>u[10]||(u[10]=[o(" \u6E2C\u8A66\u66F4\u65B0\u5C0D\u8A71\u6846 ")])),_:1})]),_:1})]),_:1}),l(y,{class:"q-mt-md"},{default:s(()=>[l(f,null,{default:s(()=>u[11]||(u[11]=[e("div",{class:"text-h6"},"\u8AAA\u660E",-1),e("ul",null,[e("li",null,"\u61C9\u7528\u6703\u6BCF30\u79D2\u81EA\u52D5\u6AA2\u67E5 Service Worker \u66F4\u65B0"),e("li",null,"\u61C9\u7528\u6703\u6BCF60\u79D2\u6AA2\u67E5\u7248\u672C\u8B8A\u66F4"),e("li",null,"\u7576\u6AA2\u6E2C\u5230\u65B0\u7248\u672C\u6642\uFF0C\u6703\u5F48\u51FA\u78BA\u8A8D\u5C0D\u8A71\u6846\u8B93\u7528\u6236\u9078\u64C7\u662F\u5426\u66F4\u65B0"),e("li",null,"\u7528\u6236\u53EF\u4EE5\u9078\u64C7\u300C\u7ACB\u5373\u66F4\u65B0\u300D\u6216\u300C\u7A0D\u5F8C\u66F4\u65B0\u300D"),e("li",null,"\u9078\u64C7\u300C\u7A0D\u5F8C\u66F4\u65B0\u300D\u6703\u572830\u5206\u9418\u5F8C\u518D\u6B21\u63D0\u9192"),e("li",null,[o("\u4F7F\u7528 "),e("code",null,"useFilenameHashes: true"),o(" \u78BA\u4FDD\u6587\u4EF6\u7248\u672C\u63A7\u5236")]),e("li",null,"\u9EDE\u64CA\u300C\u6E2C\u8A66\u66F4\u65B0\u5C0D\u8A71\u6846\u300D\u53EF\u4EE5\u9810\u89BD\u66F4\u65B0\u63D0\u793A\u7684\u6A23\u5F0F")],-1)])),_:1})]),_:1})])]),_:1}))}});export{G as default};
