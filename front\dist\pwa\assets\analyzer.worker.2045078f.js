(function(){"use strict";self.onmessage=t=>{const{type:e,data:r}=t.data;if(e==="init"){const o=JSON.parse(r.results),s=JSON.parse(r.config);H(o,s)}};function H(t,e){const r=new Map,o=new Map,s=new Map,p=t.length-1+e.lookAheadCount,g=D(t,e);let l=0;function m(u){for(let i=u;i<Math.min(t.length-e.lookAheadCount,u+100);i++){const c=N(t[i].numbers,e.firstGroupSize),j=Array.from(c);for(let n=i+1;n<t.length&&n-i<=e.maxRange;n++){const $=N(t[n].numbers,e.secondGroupSize),S=Array.from($),P=n-i;for(let a=n+1;a-n<=e.maxRange&&!(a>p);a++){const y=a-n;for(const k of j)for(const h of S){const b=`${k.join(",")}-${h.join(",")}-${P}-${y}`,G=o.get(b)||{count:0,periods:[],isPredict:!1};a<t.length?(G.count++,G.periods.push({firstPeriod:t[i].period,secondPeriod:t[n].period,targetPeriod:t[a].period})):a===p&&(G.periods.push({firstPeriod:t[i].period,secondPeriod:t[n].period}),G.isPredict=!0),o.set(b,G)}}}}for(let i=u;i<Math.min(t.length-e.lookAheadCount,u+100);i++){const c=N(t[i].numbers,e.firstGroupSize),j=Array.from(c);for(let n=i+1;n<t.length&&n-i<=e.maxRange;n++){const $=N(t[n].numbers,e.secondGroupSize),S=Array.from($),P=n-i;for(let a=n+1;a-n<=e.maxRange&&!(a>p);a++){const y=a-n;let k=[];if(a<t.length){const h=N(t[a].numbers,e.targetGroupSize);k=Array.from(h)}for(const h of j)for(const b of S){const G=`${h.join(",")}-${b.join(",")}-${P}-${y}`;if(!!(o.get(G)||{count:0,periods:[],isPredict:!1}).isPredict&&a<t.length)for(const M of k){const C=`${h.join(",")}-${b.join(",")}-${P}-${y}-${M.join(",")}`,v=r.get(C);if(v)v.targetMatches++;else{const B={firstNumbers:h,secondNumbers:b,targetNumbers:M,gap:P,targetGap:y,targetMatches:1,targetProbability:0,rank:0,consecutiveHits:0};r.set(C,B)}const A=`${h.join(",")}-${b.join(",")}-${P}-${y}-${M.join(",")}`,L=t[a].period;s.has(A)||s.set(A,[]);const w=s.get(A);w&&w.push(L)}}l++,z(l,g)}}}z(l,g),u+100<t.length-e.lookAheadCount?setTimeout(()=>m(u+100),0):f()}function f(){const u=[],d=new Map;for(const c of r.values()){const j=`${c.firstNumbers.join(",")}-${c.secondNumbers.join(",")}-${c.gap}-${c.targetGap}`,n=o.get(j);if(!(n!=null&&n.isPredict)||!n||n.count<=0||(c.targetProbability=c.targetMatches/n.count,c.consecutiveHits=O(c,n,s),c.consecutiveHits<1))continue;d.has(c.targetMatches)||d.set(c.targetMatches,[]);const $=d.get(c.targetMatches);$&&$.push(c),u.push(c)}const i=x(u,o);postMessage({type:"complete",data:i,occurrences:o,matchData:d})}m(0)}function O(t,e,r){const o=e.periods.filter(f=>f.targetPeriod!==void 0&&f.targetPeriod!==null&&f.targetPeriod.trim()!=="").map(f=>f.targetPeriod);if(o.length===0)return 0;const s=o.sort((f,u)=>{const d=parseInt(f),i=parseInt(u);return isNaN(d)||isNaN(i)?f.localeCompare(u):i-d}),p=`${t.firstNumbers.join(",")}-${t.secondNumbers.join(",")}-${t.gap}-${t.targetGap}-${t.targetNumbers.join(",")}`,g=r.get(p)||[];if(g.length===0)return 0;const l=new Set(g);let m=0;for(const f of s)if(l.has(f))m++;else break;return m}function*R(t,e,r=0,o=[]){if(o.length===e){yield[...o];return}for(let s=r;s<t.length;s++)o.push(t[s]),yield*R(t,e,s+1,o),o.pop()}function N(t,e){return R(t,e)}function D(t,e){const r=t.length-1+e.lookAheadCount;let o=0;for(let s=0;s<t.length-e.lookAheadCount;s++)for(let p=s+1;p<t.length&&p-s<=e.maxRange;p++)for(let g=p+1;g<t.length&&g-p<=e.maxRange&&!(g>r);g++)o++;return o}function x(t,e){return t.sort((r,o)=>{var u,d;const s=o.targetProbability-r.targetProbability;if(s!==0)return s;const p=((u=e.get(r.firstNumbers.join(",")+"-"+r.secondNumbers.join(",")+"-"+r.gap))==null?void 0:u.count)||0,l=(((d=e.get(o.firstNumbers.join(",")+"-"+o.secondNumbers.join(",")+"-"+o.gap))==null?void 0:d.count)||0)-p;if(l!==0)return l;const m=r.firstNumbers&&r.firstNumbers.length>0?r.firstNumbers[0]:1/0,f=o.firstNumbers&&o.firstNumbers.length>0?o.firstNumbers[0]:1/0;return m-f}),t.map((r,o)=>(r.rank=o+1,r))}function z(t,e){postMessage({type:"progress",data:{stage:"processing",progress:t,total:e}})}})();
