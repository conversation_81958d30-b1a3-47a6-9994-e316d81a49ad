import{C as o,ar as n,at as d,c as l,h as i,Q as u,t as c}from"./index.3dd97541.js";var q=o({name:"QList",props:{...n,bordered:Bo<PERSON>an,dense:<PERSON><PERSON><PERSON>,separator:<PERSON><PERSON><PERSON>,padding:<PERSON><PERSON><PERSON>,tag:{type:String,default:"div"}},setup(e,{slots:a}){const t=c(),s=d(e,t.proxy.$q),r=l(()=>"q-list"+(e.bordered===!0?" q-list--bordered":"")+(e.dense===!0?" q-list--dense":"")+(e.separator===!0?" q-list--separator":"")+(s.value===!0?" q-list--dark":"")+(e.padding===!0?" q-list--padding":""));return()=>i(e.tag,{class:r.value},u(a.default))}});export{q as Q};
