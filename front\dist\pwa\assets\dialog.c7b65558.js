import{ag as n}from"./index.bf55edc3.js";const f=()=>({showMessage:({message:a,title:o="\u63D0\u793A",timeout:l=1500,persistent:t=!1,color:i="primary",ok:s,onRedirect:e})=>{const r=n.create({title:o,message:a,color:i,persistent:t,html:!0,style:{display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",textAlign:"center"},ok:{label:"\u78BA\u5B9A",color:"primary"},cancel:l>0?void 0:{label:"\u53D6\u6D88",color:"negative"}}).onOk(()=>{s?s():e&&e()});l>0&&setTimeout(()=>{r.hide(),e&&e()},l)}});export{f as u};
