import{C as Pe,aJ as We,aK as yt,aL as bt,aM as wt,ar as Ct,aN as Dt,at as Vt,aO as Kt,c as d,ax as Nt,t as Re,h as V,a5 as _e,aP as $t,aQ as jt,ab as oe,aq as Qt,aR as Wt,U as Ut,as as Xt,r as E,aS as Yt,au as Jt,aT as Zt,av as Gt,aU as el,w as ae,aV as tl,ao as ll,aW as ul,aX as st,W as nl,aY as ol,Q as al,T as il,a as Ue,aZ as rl,ac as sl,a_ as cl,V as dl,n as ne,a$ as fl,x as vl,o as ml,f as Sl,b0 as gl,b1 as hl,b2 as ct,b3 as ze,d as yl,e as bl,am as je,b4 as wl,an as Ve,aH as Cl,b5 as Vl,b6 as xl,F as kl}from"./index.bf55edc3.js";import{n as dt,a as ql,c as Al,Q as Ol}from"./QItem.955fd840.js";import{u as zl,v as ft,a as Fl,b as Ml,c as Tl,p as vt,r as mt,s as pl,d as Il}from"./position-engine.8228ce32.js";var Bl=Pe({name:"QField",inheritAttrs:!1,props:{...We,tag:{type:String,default:"label"}},emits:yt,setup(){return bt(wt({tagProp:!0}))}});const _l={xs:8,sm:10,md:14,lg:20,xl:24};var El=Pe({name:"QChip",props:{...Ct,...Dt,dense:Boolean,icon:String,iconRight:String,iconRemove:String,iconSelected:String,label:[String,Number],color:String,textColor:String,modelValue:{type:Boolean,default:!0},selected:{type:Boolean,default:null},square:Boolean,outline:Boolean,clickable:Boolean,removable:Boolean,removeAriaLabel:String,tabindex:[String,Number],disable:Boolean,ripple:{type:[Boolean,Object],default:!0}},emits:["update:modelValue","update:selected","remove","click"],setup(e,{slots:c,emit:r}){const{proxy:{$q:b}}=Re(),k=Vt(e,b),i=Kt(e,_l),z=d(()=>e.selected===!0||e.icon!==void 0),h=d(()=>e.selected===!0?e.iconSelected||b.iconSet.chip.selected:e.icon),y=d(()=>e.iconRemove||b.iconSet.chip.remove),F=d(()=>e.disable===!1&&(e.clickable===!0||e.selected!==null)),s=d(()=>{const f=e.outline===!0&&e.color||e.textColor;return"q-chip row inline no-wrap items-center"+(e.outline===!1&&e.color!==void 0?` bg-${e.color}`:"")+(f?` text-${f} q-chip--colored`:"")+(e.disable===!0?" disabled":"")+(e.dense===!0?" q-chip--dense":"")+(e.outline===!0?" q-chip--outline":"")+(e.selected===!0?" q-chip--selected":"")+(F.value===!0?" q-chip--clickable cursor-pointer non-selectable q-hoverable":"")+(e.square===!0?" q-chip--square":"")+(k.value===!0?" q-chip--dark q-dark":"")}),S=d(()=>{const f=e.disable===!0?{tabindex:-1,"aria-disabled":"true"}:{tabindex:e.tabindex||0},L={...f,role:"button","aria-hidden":"false","aria-label":e.removeAriaLabel||b.lang.label.remove};return{chip:f,remove:L}});function w(f){f.keyCode===13&&x(f)}function x(f){e.disable||(r("update:selected",!e.selected),r("click",f))}function M(f){(f.keyCode===void 0||f.keyCode===13)&&(oe(f),e.disable===!1&&(r("update:modelValue",!1),r("remove")))}function D(){const f=[];F.value===!0&&f.push(V("div",{class:"q-focus-helper"})),z.value===!0&&f.push(V(_e,{class:"q-chip__icon q-chip__icon--left",name:h.value}));const L=e.label!==void 0?[V("div",{class:"ellipsis"},[e.label])]:void 0;return f.push(V("div",{class:"q-chip__content col row no-wrap items-center q-anchor--skip"},$t(c.default,L))),e.iconRight&&f.push(V(_e,{class:"q-chip__icon q-chip__icon--right",name:e.iconRight})),e.removable===!0&&f.push(V(_e,{class:"q-chip__icon q-chip__icon--remove cursor-pointer",name:y.value,...S.value.remove,onClick:M,onKeyup:M})),f}return()=>{if(e.modelValue===!1)return;const f={class:s.value,style:i.value};return F.value===!0&&Object.assign(f,S.value.chip,{onClick:x,onKeyup:w}),Nt("div",f,D(),"ripple",e.ripple!==!1&&e.disable!==!0,()=>[[jt,e.ripple]])}}}),Pl=Pe({name:"QMenu",inheritAttrs:!1,props:{...zl,...Qt,...Ct,...Wt,persistent:Boolean,autoClose:Boolean,separateClosePopup:Boolean,noRouteDismiss:Boolean,noRefocus:Boolean,noFocus:Boolean,fit:Boolean,cover:Boolean,square:Boolean,anchor:{type:String,validator:ft},self:{type:String,validator:ft},offset:{type:Array,validator:Fl},scrollTarget:Ut,touchPosition:Boolean,maxHeight:{type:String,default:null},maxWidth:{type:String,default:null}},emits:[...Xt,"click","escapeKey"],setup(e,{slots:c,emit:r,attrs:b}){let k=null,i,z,h;const y=Re(),{proxy:F}=y,{$q:s}=F,S=E(null),w=E(!1),x=d(()=>e.persistent!==!0&&e.noRouteDismiss!==!0),M=Vt(e,s),{registerTick:D,removeTick:f}=Yt(),{registerTimeout:L}=Jt(),{transitionProps:W,transitionStyle:P}=Zt(e),{localScrollTarget:T,changeScrollEvent:me,unconfigureScrollTarget:p}=Ml(e,g),{anchorEl:N,canShow:G}=Tl({showing:w}),{hide:te}=Gt({showing:w,canShow:G,handleShow:se,handleHide:l,hideOnRouteChange:x,processOnMount:!0}),{showPortal:U,hidePortal:ie,renderPortal:K}=el(y,S,B,"menu"),ee={anchorEl:N,innerRef:S,onClickOutside(u){if(e.persistent!==!0&&w.value===!0)return te(u),(u.type==="touchstart"||u.target.classList.contains("q-dialog__backdrop"))&&oe(u),!0}},re=d(()=>vt(e.anchor||(e.cover===!0?"center middle":"bottom start"),s.lang.rtl)),le=d(()=>e.cover===!0?re.value:vt(e.self||"top start",s.lang.rtl)),j=d(()=>(e.square===!0?" q-menu--square":"")+(M.value===!0?" q-menu--dark q-dark":"")),ke=d(()=>e.autoClose===!0?{onClick:q}:{}),ue=d(()=>w.value===!0&&e.persistent!==!0);ae(ue,u=>{u===!0?(rl(C),Il(ee)):(st(C),mt(ee))});function X(){sl(()=>{let u=S.value;u&&u.contains(document.activeElement)!==!0&&(u=u.querySelector("[autofocus][tabindex], [data-autofocus][tabindex]")||u.querySelector("[autofocus] [tabindex], [data-autofocus] [tabindex]")||u.querySelector("[autofocus], [data-autofocus]")||u,u.focus({preventScroll:!0}))})}function se(u){if(k=e.noRefocus===!1?document.activeElement:null,tl(I),U(),g(),i=void 0,u!==void 0&&(e.touchPosition||e.contextMenu)){const R=ll(u);if(R.left!==void 0){const{top:Y,left:Se}=N.value.getBoundingClientRect();i={left:R.left-Se,top:R.top-Y}}}z===void 0&&(z=ae(()=>s.screen.width+"|"+s.screen.height+"|"+e.self+"|"+e.anchor+"|"+s.lang.rtl,m)),e.noFocus!==!0&&document.activeElement.blur(),D(()=>{m(),e.noFocus!==!0&&X()}),L(()=>{s.platform.is.ios===!0&&(h=e.autoClose,S.value.click()),m(),U(!0),r("show",u)},e.transitionDuration)}function l(u){f(),ie(),o(!0),k!==null&&(u===void 0||u.qClickOutside!==!0)&&(((u&&u.type.indexOf("key")===0?k.closest('[tabindex]:not([tabindex^="-"])'):void 0)||k).focus(),k=null),L(()=>{ie(!0),r("hide",u)},e.transitionDuration)}function o(u){i=void 0,z!==void 0&&(z(),z=void 0),(u===!0||w.value===!0)&&(ul(I),p(),mt(ee),st(C)),u!==!0&&(k=null)}function g(){(N.value!==null||e.scrollTarget!==void 0)&&(T.value=nl(N.value,e.scrollTarget),me(T.value,m))}function q(u){h!==!0?(ol(F,u),r("click",u)):h=!1}function I(u){ue.value===!0&&e.noFocus!==!0&&cl(S.value,u.target)!==!0&&X()}function C(u){r("escapeKey"),te(u)}function m(){pl({targetEl:S.value,offset:e.offset,anchorEl:N.value,anchorOrigin:re.value,selfOrigin:le.value,absoluteOffset:i,fit:e.fit,cover:e.cover,maxHeight:e.maxHeight,maxWidth:e.maxWidth})}function B(){return V(il,W.value,()=>w.value===!0?V("div",{role:"menu",...b,ref:S,tabindex:-1,class:["q-menu q-position-engine scroll"+j.value,b.class],style:[b.style,P.value],...ke.value},al(c.default)):null)}return Ue(o),Object.assign(F,{focus:X,updatePosition:m}),K}});let Ee=!1;{const e=document.createElement("div");e.setAttribute("dir","rtl"),Object.assign(e.style,{width:"1px",height:"1px",overflow:"auto"});const c=document.createElement("div");Object.assign(c.style,{width:"1000px",height:"1px"}),document.body.appendChild(e),e.appendChild(c),e.scrollLeft=-1e3,Ee=e.scrollLeft>=0,e.remove()}const Q=1e3,Rl=["start","center","end","start-force","center-force","end-force"],xt=Array.prototype.filter,Hl=window.getComputedStyle(document.body).overflowAnchor===void 0?dl:function(e,c){e!==null&&(e._qOverflowAnimationFrame!==void 0&&cancelAnimationFrame(e._qOverflowAnimationFrame),e._qOverflowAnimationFrame=requestAnimationFrame(()=>{if(e===null)return;e._qOverflowAnimationFrame=void 0;const r=e.children||[];xt.call(r,k=>k.dataset&&k.dataset.qVsAnchor!==void 0).forEach(k=>{delete k.dataset.qVsAnchor});const b=r[c];b&&b.dataset&&(b.dataset.qVsAnchor="")}))};function xe(e,c){return e+c}function Qe(e,c,r,b,k,i,z,h){const y=e===window?document.scrollingElement||document.documentElement:e,F=k===!0?"offsetWidth":"offsetHeight",s={scrollStart:0,scrollViewSize:-z-h,scrollMaxSize:0,offsetStart:-z,offsetEnd:-h};if(k===!0?(e===window?(s.scrollStart=window.pageXOffset||window.scrollX||document.body.scrollLeft||0,s.scrollViewSize+=document.documentElement.clientWidth):(s.scrollStart=y.scrollLeft,s.scrollViewSize+=y.clientWidth),s.scrollMaxSize=y.scrollWidth,i===!0&&(s.scrollStart=(Ee===!0?s.scrollMaxSize-s.scrollViewSize:0)-s.scrollStart)):(e===window?(s.scrollStart=window.pageYOffset||window.scrollY||document.body.scrollTop||0,s.scrollViewSize+=document.documentElement.clientHeight):(s.scrollStart=y.scrollTop,s.scrollViewSize+=y.clientHeight),s.scrollMaxSize=y.scrollHeight),r!==null)for(let S=r.previousElementSibling;S!==null;S=S.previousElementSibling)S.classList.contains("q-virtual-scroll--skip")===!1&&(s.offsetStart+=S[F]);if(b!==null)for(let S=b.nextElementSibling;S!==null;S=S.nextElementSibling)S.classList.contains("q-virtual-scroll--skip")===!1&&(s.offsetEnd+=S[F]);if(c!==e){const S=y.getBoundingClientRect(),w=c.getBoundingClientRect();k===!0?(s.offsetStart+=w.left-S.left,s.offsetEnd-=w.width):(s.offsetStart+=w.top-S.top,s.offsetEnd-=w.height),e!==window&&(s.offsetStart+=s.scrollStart),s.offsetEnd+=s.scrollMaxSize-s.offsetStart}return s}function St(e,c,r,b){c==="end"&&(c=(e===window?document.body:e)[r===!0?"scrollWidth":"scrollHeight"]),e===window?r===!0?(b===!0&&(c=(Ee===!0?document.body.scrollWidth-document.documentElement.clientWidth:0)-c),window.scrollTo(c,window.pageYOffset||window.scrollY||document.body.scrollTop||0)):window.scrollTo(window.pageXOffset||window.scrollX||document.body.scrollLeft||0,c):r===!0?(b===!0&&(c=(Ee===!0?e.scrollWidth-e.offsetWidth:0)-c),e.scrollLeft=c):e.scrollTop=c}function Fe(e,c,r,b){if(r>=b)return 0;const k=c.length,i=Math.floor(r/Q),z=Math.floor((b-1)/Q)+1;let h=e.slice(i,z).reduce(xe,0);return r%Q!==0&&(h-=c.slice(i*Q,r).reduce(xe,0)),b%Q!==0&&b!==k&&(h-=c.slice(b,z*Q).reduce(xe,0)),h}const kt={virtualScrollSliceSize:{type:[Number,String],default:10},virtualScrollSliceRatioBefore:{type:[Number,String],default:1},virtualScrollSliceRatioAfter:{type:[Number,String],default:1},virtualScrollItemSize:{type:[Number,String],default:24},virtualScrollStickySizeStart:{type:[Number,String],default:0},virtualScrollStickySizeEnd:{type:[Number,String],default:0},tableColspan:[Number,String]},Ql=Object.keys(kt),gt={virtualScrollHorizontal:Boolean,onVirtualScroll:Function,...kt};function Ll({virtualScrollLength:e,getVirtualScrollTarget:c,getVirtualScrollEl:r,virtualScrollItemSizeComputed:b}){const k=Re(),{props:i,emit:z,proxy:h}=k,{$q:y}=h;let F,s,S,w=[],x;const M=E(0),D=E(0),f=E({}),L=E(null),W=E(null),P=E(null),T=E({from:0,to:0}),me=d(()=>i.tableColspan!==void 0?i.tableColspan:100);b===void 0&&(b=d(()=>i.virtualScrollItemSize));const p=d(()=>b.value+";"+i.virtualScrollHorizontal),N=d(()=>p.value+";"+i.virtualScrollSliceRatioBefore+";"+i.virtualScrollSliceRatioAfter);ae(N,()=>{j()}),ae(p,G);function G(){le(s,!0)}function te(l){le(l===void 0?s:l)}function U(l,o){const g=c();if(g==null||g.nodeType===8)return;const q=Qe(g,r(),L.value,W.value,i.virtualScrollHorizontal,y.lang.rtl,i.virtualScrollStickySizeStart,i.virtualScrollStickySizeEnd);S!==q.scrollViewSize&&j(q.scrollViewSize),K(g,q,Math.min(e.value-1,Math.max(0,parseInt(l,10)||0)),0,Rl.indexOf(o)!==-1?o:s!==-1&&l>s?"end":"start")}function ie(){const l=c();if(l==null||l.nodeType===8)return;const o=Qe(l,r(),L.value,W.value,i.virtualScrollHorizontal,y.lang.rtl,i.virtualScrollStickySizeStart,i.virtualScrollStickySizeEnd),g=e.value-1,q=o.scrollMaxSize-o.offsetStart-o.offsetEnd-D.value;if(F===o.scrollStart)return;if(o.scrollMaxSize<=0){K(l,o,0,0);return}S!==o.scrollViewSize&&j(o.scrollViewSize),ee(T.value.from);const I=Math.floor(o.scrollMaxSize-Math.max(o.scrollViewSize,o.offsetEnd)-Math.min(x[g],o.scrollViewSize/2));if(I>0&&Math.ceil(o.scrollStart)>=I){K(l,o,g,o.scrollMaxSize-o.offsetEnd-w.reduce(xe,0));return}let C=0,m=o.scrollStart-o.offsetStart,B=m;if(m<=q&&m+o.scrollViewSize>=M.value)m-=M.value,C=T.value.from,B=m;else for(let u=0;m>=w[u]&&C<g;u++)m-=w[u],C+=Q;for(;m>0&&C<g;)m-=x[C],m>-o.scrollViewSize?(C++,B=m):B=x[C]+m;K(l,o,C,B)}function K(l,o,g,q,I){const C=typeof I=="string"&&I.indexOf("-force")!==-1,m=C===!0?I.replace("-force",""):I,B=m!==void 0?m:"start";let u=Math.max(0,g-f.value[B]),R=u+f.value.total;R>e.value&&(R=e.value,u=Math.max(0,R-f.value.total)),F=o.scrollStart;const Y=u!==T.value.from||R!==T.value.to;if(Y===!1&&m===void 0){ue(g);return}const{activeElement:Se}=document,J=P.value;Y===!0&&J!==null&&J!==Se&&J.contains(Se)===!0&&(J.addEventListener("focusout",re),setTimeout(()=>{J!==null&&J.removeEventListener("focusout",re)})),Hl(J,g-u);const Me=m!==void 0?x.slice(u,g).reduce(xe,0):0;if(Y===!0){const ce=R>=T.value.from&&u<=T.value.to?T.value.to:R;T.value={from:u,to:ce},M.value=Fe(w,x,0,u),D.value=Fe(w,x,R,e.value),requestAnimationFrame(()=>{T.value.to!==R&&F===o.scrollStart&&(T.value={from:T.value.from,to:R},D.value=Fe(w,x,R,e.value))})}requestAnimationFrame(()=>{if(F!==o.scrollStart)return;Y===!0&&ee(u);const ce=x.slice(u,g).reduce(xe,0),de=ce+o.offsetStart+M.value,Te=de+x[g];let qe=de+q;if(m!==void 0){const He=ce-Me,Ae=o.scrollStart+He;qe=C!==!0&&Ae<de&&Te<Ae+o.scrollViewSize?Ae:m==="end"?Te-o.scrollViewSize:de-(m==="start"?0:Math.round((o.scrollViewSize-x[g])/2))}F=qe,St(l,qe,i.virtualScrollHorizontal,y.lang.rtl),ue(g)})}function ee(l){const o=P.value;if(o){const g=xt.call(o.children,u=>u.classList&&u.classList.contains("q-virtual-scroll--skip")===!1),q=g.length,I=i.virtualScrollHorizontal===!0?u=>u.getBoundingClientRect().width:u=>u.offsetHeight;let C=l,m,B;for(let u=0;u<q;){for(m=I(g[u]),u++;u<q&&g[u].classList.contains("q-virtual-scroll--with-prev")===!0;)m+=I(g[u]),u++;B=m-x[C],B!==0&&(x[C]+=B,w[Math.floor(C/Q)]+=B),C++}}}function re(){P.value!==null&&P.value!==void 0&&P.value.focus()}function le(l,o){const g=1*b.value;(o===!0||Array.isArray(x)===!1)&&(x=[]);const q=x.length;x.length=e.value;for(let C=e.value-1;C>=q;C--)x[C]=g;const I=Math.floor((e.value-1)/Q);w=[];for(let C=0;C<=I;C++){let m=0;const B=Math.min((C+1)*Q,e.value);for(let u=C*Q;u<B;u++)m+=x[u];w.push(m)}s=-1,F=void 0,M.value=Fe(w,x,0,T.value.from),D.value=Fe(w,x,T.value.to,e.value),l>=0?(ee(T.value.from),ne(()=>{U(l)})):X()}function j(l){if(l===void 0&&typeof window!="undefined"){const m=c();m!=null&&m.nodeType!==8&&(l=Qe(m,r(),L.value,W.value,i.virtualScrollHorizontal,y.lang.rtl,i.virtualScrollStickySizeStart,i.virtualScrollStickySizeEnd).scrollViewSize)}S=l;const o=parseFloat(i.virtualScrollSliceRatioBefore)||0,g=parseFloat(i.virtualScrollSliceRatioAfter)||0,q=1+o+g,I=l===void 0||l<=0?1:Math.ceil(l/b.value),C=Math.max(1,I,Math.ceil((i.virtualScrollSliceSize>0?i.virtualScrollSliceSize:10)/q));f.value={total:Math.ceil(C*q),start:Math.ceil(C*o),center:Math.ceil(C*(.5+o)),end:Math.ceil(C*(1+o)),view:I}}function ke(l,o){const g=i.virtualScrollHorizontal===!0?"width":"height",q={["--q-virtual-scroll-item-"+g]:b.value+"px"};return[l==="tbody"?V(l,{class:"q-virtual-scroll__padding",key:"before",ref:L},[V("tr",[V("td",{style:{[g]:`${M.value}px`,...q},colspan:me.value})])]):V(l,{class:"q-virtual-scroll__padding",key:"before",ref:L,style:{[g]:`${M.value}px`,...q}}),V(l,{class:"q-virtual-scroll__content",key:"content",ref:P,tabindex:-1},o.flat()),l==="tbody"?V(l,{class:"q-virtual-scroll__padding",key:"after",ref:W},[V("tr",[V("td",{style:{[g]:`${D.value}px`,...q},colspan:me.value})])]):V(l,{class:"q-virtual-scroll__padding",key:"after",ref:W,style:{[g]:`${D.value}px`,...q}})]}function ue(l){s!==l&&(i.onVirtualScroll!==void 0&&z("virtualScroll",{index:l,from:T.value.from,to:T.value.to-1,direction:l<s?"decrease":"increase",ref:h}),s=l)}j();const X=fl(ie,y.platform.is.ios===!0?120:35);vl(()=>{j()});let se=!1;return ml(()=>{se=!0}),Sl(()=>{if(se!==!0)return;const l=c();F!==void 0&&l!==void 0&&l!==null&&l.nodeType!==8?St(l,F,i.virtualScrollHorizontal,y.lang.rtl):U(s)}),Ue(()=>{X.cancel()}),Object.assign(h,{scrollTo:U,reset:G,refresh:te}),{virtualScrollSliceRange:T,virtualScrollSliceSizeComputed:f,setVirtualScrollSize:j,onVirtualScrollEvt:X,localResetVirtualScroll:le,padVirtualScroll:ke,scrollTo:U,reset:G,refresh:te}}const ht=e=>["add","add-unique","toggle"].includes(e),Dl=".*+?^${}()|[]\\",Kl=Object.keys(We);var Wl=Pe({name:"QSelect",inheritAttrs:!1,props:{...gt,...gl,...We,modelValue:{required:!0},multiple:Boolean,displayValue:[String,Number],displayValueHtml:Boolean,dropdownIcon:String,options:{type:Array,default:()=>[]},optionValue:[Function,String],optionLabel:[Function,String],optionDisable:[Function,String],hideSelected:Boolean,hideDropdownIcon:Boolean,fillInput:Boolean,maxValues:[Number,String],optionsDense:Boolean,optionsDark:{type:Boolean,default:null},optionsSelectedClass:String,optionsHtml:Boolean,optionsCover:Boolean,menuShrink:Boolean,menuAnchor:String,menuSelf:String,menuOffset:Array,popupContentClass:String,popupContentStyle:[String,Array,Object],popupNoRouteDismiss:Boolean,useInput:Boolean,useChips:Boolean,newValueMode:{type:String,validator:ht},mapOptions:Boolean,emitValue:Boolean,inputDebounce:{type:[Number,String],default:500},inputClass:[Array,String,Object],inputStyle:[Array,String,Object],tabindex:{type:[String,Number],default:0},autocomplete:String,transitionShow:{},transitionHide:{},transitionDuration:{},behavior:{type:String,validator:e=>["default","menu","dialog"].includes(e),default:"default"},virtualScrollItemSize:gt.virtualScrollItemSize.type,onNewValue:Function,onFilter:Function},emits:[...yt,"add","remove","inputValue","keyup","keypress","keydown","popupShow","popupHide","filterAbort"],setup(e,{slots:c,emit:r}){const{proxy:b}=Re(),{$q:k}=b,i=E(!1),z=E(!1),h=E(-1),y=E(""),F=E(!1),s=E(!1);let S=null,w=null,x,M,D,f=null,L,W,P,T;const me=E(null),p=E(null),N=E(null),G=E(null),te=E(null),U=hl(e),ie=wl(nt),K=d(()=>Array.isArray(e.options)?e.options.length:0),ee=d(()=>e.virtualScrollItemSize===void 0?e.optionsDense===!0?24:48:e.virtualScrollItemSize),{virtualScrollSliceRange:re,virtualScrollSliceSizeComputed:le,localResetVirtualScroll:j,padVirtualScroll:ke,onVirtualScrollEvt:ue,scrollTo:X,setVirtualScrollSize:se}=Ll({virtualScrollLength:K,getVirtualScrollTarget:zt,getVirtualScrollEl:lt,virtualScrollItemSizeComputed:ee}),l=wt(),o=d(()=>{const t=e.mapOptions===!0&&e.multiple!==!0,a=e.modelValue!==void 0&&(e.modelValue!==null||t===!0)?e.multiple===!0&&Array.isArray(e.modelValue)?e.modelValue:[e.modelValue]:[];if(e.mapOptions===!0&&Array.isArray(e.options)===!0){const n=e.mapOptions===!0&&x!==void 0?x:[],v=a.map(O=>Ot(O,n));return e.modelValue===null&&t===!0?v.filter(O=>O!==null):v}return a}),g=d(()=>{const t={};return Kl.forEach(a=>{const n=e[a];n!==void 0&&(t[a]=n)}),t}),q=d(()=>e.optionsDark===null?l.isDark.value:e.optionsDark),I=d(()=>ct(o.value)),C=d(()=>{let t="q-field__input q-placeholder col";return e.hideSelected===!0||o.value.length===0?[t,e.inputClass]:(t+=" q-field__input--padding",e.inputClass===void 0?t:[t,e.inputClass])}),m=d(()=>(e.virtualScrollHorizontal===!0?"q-virtual-scroll--horizontal":"")+(e.popupContentClass?" "+e.popupContentClass:"")),B=d(()=>K.value===0),u=d(()=>o.value.map(t=>$.value(t)).join(", ")),R=d(()=>e.displayValue!==void 0?e.displayValue:u.value),Y=d(()=>e.optionsHtml===!0?()=>!0:t=>t!=null&&t.html===!0),Se=d(()=>e.displayValueHtml===!0||e.displayValue===void 0&&(e.optionsHtml===!0||o.value.some(Y.value))),J=d(()=>l.focused.value===!0?e.tabindex:-1),Me=d(()=>{const t={tabindex:e.tabindex,role:"combobox","aria-label":e.label,"aria-readonly":e.readonly===!0?"true":"false","aria-autocomplete":e.useInput===!0?"list":"none","aria-expanded":i.value===!0?"true":"false","aria-controls":`${l.targetUid.value}_lb`};return h.value>=0&&(t["aria-activedescendant"]=`${l.targetUid.value}_${h.value}`),t}),ce=d(()=>({id:`${l.targetUid.value}_lb`,role:"listbox","aria-multiselectable":e.multiple===!0?"true":"false"})),de=d(()=>o.value.map((t,a)=>({index:a,opt:t,html:Y.value(t),selected:!0,removeAtIndex:At,toggleOption:fe,tabindex:J.value}))),Te=d(()=>{if(K.value===0)return[];const{from:t,to:a}=re.value;return e.options.slice(t,a).map((n,v)=>{const O=ge.value(n)===!0,A=Ke(n)===!0,H=t+v,_={clickable:!0,active:A,activeClass:Ae.value,manualFocus:!0,focused:!1,disable:O,tabindex:-1,dense:e.optionsDense,dark:q.value,role:"option","aria-selected":A===!0?"true":"false",id:`${l.targetUid.value}_${H}`,onClick:()=>{fe(n)}};return O!==!0&&(h.value===H&&(_.focused=!0),k.platform.is.desktop===!0&&(_.onMousemove=()=>{i.value===!0&&he(H)})),{index:H,opt:n,html:Y.value(n),label:$.value(n),selected:_.active,focused:_.focused,toggleOption:fe,setOptionIndex:he,itemProps:_}})}),qe=d(()=>e.dropdownIcon!==void 0?e.dropdownIcon:k.iconSet.arrow.dropdown),He=d(()=>e.optionsCover===!1&&e.outlined!==!0&&e.standout!==!0&&e.borderless!==!0&&e.rounded!==!0),Ae=d(()=>e.optionsSelectedClass!==void 0?e.optionsSelectedClass:e.color!==void 0?`text-${e.color}`:""),Z=d(()=>De(e.optionValue,"value")),$=d(()=>De(e.optionLabel,"label")),ge=d(()=>De(e.optionDisable,"disable")),pe=d(()=>o.value.map(t=>Z.value(t))),qt=d(()=>{const t={onInput:nt,onChange:ie,onKeydown:tt,onKeyup:Ge,onKeypress:et,onFocus:Je,onClick(a){M===!0&&Ve(a)}};return t.onCompositionstart=t.onCompositionupdate=t.onCompositionend=ie,t});ae(o,t=>{x=t,e.useInput===!0&&e.fillInput===!0&&e.multiple!==!0&&l.innerLoading.value!==!0&&(z.value!==!0&&i.value!==!0||I.value!==!0)&&(D!==!0&&Ce(),(z.value===!0||i.value===!0)&&ye(""))},{immediate:!0}),ae(()=>e.fillInput,Ce),ae(i,Ne),ae(K,Lt);function Xe(t){return e.emitValue===!0?Z.value(t):t}function Le(t){if(t!==-1&&t<o.value.length)if(e.multiple===!0){const a=e.modelValue.slice();r("remove",{index:t,value:a.splice(t,1)[0]}),r("update:modelValue",a)}else r("update:modelValue",null)}function At(t){Le(t),l.focus()}function Ye(t,a){const n=Xe(t);if(e.multiple!==!0){e.fillInput===!0&&Oe($.value(t),!0,!0),r("update:modelValue",n);return}if(o.value.length===0){r("add",{index:0,value:n}),r("update:modelValue",e.multiple===!0?[n]:n);return}if(a===!0&&Ke(t)===!0||e.maxValues!==void 0&&e.modelValue.length>=e.maxValues)return;const v=e.modelValue.slice();r("add",{index:v.length,value:n}),v.push(n),r("update:modelValue",v)}function fe(t,a){if(l.editable.value!==!0||t===void 0||ge.value(t)===!0)return;const n=Z.value(t);if(e.multiple!==!0){a!==!0&&(Oe(e.fillInput===!0?$.value(t):"",!0,!0),ve()),p.value!==null&&p.value.focus(),(o.value.length===0||ze(Z.value(o.value[0]),n)!==!0)&&r("update:modelValue",e.emitValue===!0?n:t);return}if((M!==!0||F.value===!0)&&l.focus(),Je(),o.value.length===0){const A=e.emitValue===!0?n:t;r("add",{index:0,value:A}),r("update:modelValue",e.multiple===!0?[A]:A);return}const v=e.modelValue.slice(),O=pe.value.findIndex(A=>ze(A,n));if(O!==-1)r("remove",{index:O,value:v.splice(O,1)[0]});else{if(e.maxValues!==void 0&&v.length>=e.maxValues)return;const A=e.emitValue===!0?n:t;r("add",{index:v.length,value:A}),v.push(A)}r("update:modelValue",v)}function he(t){if(k.platform.is.desktop!==!0)return;const a=t!==-1&&t<K.value?t:-1;h.value!==a&&(h.value=a)}function Ie(t=1,a){if(i.value===!0){let n=h.value;do n=dt(n+t,-1,K.value-1);while(n!==-1&&n!==h.value&&ge.value(e.options[n])===!0);h.value!==n&&(he(n),X(n),a!==!0&&e.useInput===!0&&e.fillInput===!0&&Be(n>=0?$.value(e.options[n]):L,!0))}}function Ot(t,a){const n=v=>ze(Z.value(v),t);return e.options.find(n)||a.find(n)||t}function De(t,a){const n=t!==void 0?t:a;return typeof n=="function"?n:v=>v!==null&&typeof v=="object"&&n in v?v[n]:v}function Ke(t){const a=Z.value(t);return pe.value.find(n=>ze(n,a))!==void 0}function Je(t){e.useInput===!0&&p.value!==null&&(t===void 0||p.value===t.target&&t.target.value===u.value)&&p.value.select()}function Ze(t){Cl(t,27)===!0&&i.value===!0&&(Ve(t),ve(),Ce()),r("keyup",t)}function Ge(t){const{value:a}=t.target;if(t.keyCode!==void 0){Ze(t);return}if(t.target.value="",S!==null&&(clearTimeout(S),S=null),w!==null&&(clearTimeout(w),w=null),Ce(),typeof a=="string"&&a.length!==0){const n=a.toLocaleLowerCase(),v=A=>{const H=e.options.find(_=>A.value(_).toLocaleLowerCase()===n);return H===void 0?!1:(o.value.indexOf(H)===-1?fe(H):ve(),!0)},O=A=>{v(Z)!==!0&&(v($)===!0||A===!0||ye(a,!0,()=>O(!0)))};O()}else l.clearValue(t)}function et(t){r("keypress",t)}function tt(t){if(r("keydown",t),Vl(t)===!0)return;const a=y.value.length!==0&&(e.newValueMode!==void 0||e.onNewValue!==void 0),n=t.shiftKey!==!0&&e.multiple!==!0&&(h.value!==-1||a===!0);if(t.keyCode===27){je(t);return}if(t.keyCode===9&&n===!1){be();return}if(t.target===void 0||t.target.id!==l.targetUid.value||l.editable.value!==!0)return;if(t.keyCode===40&&l.innerLoading.value!==!0&&i.value===!1){oe(t),we();return}if(t.keyCode===8&&(e.useChips===!0||e.clearable===!0)&&e.hideSelected!==!0&&y.value.length===0){e.multiple===!0&&Array.isArray(e.modelValue)===!0?Le(e.modelValue.length-1):e.multiple!==!0&&e.modelValue!==null&&r("update:modelValue",null);return}(t.keyCode===35||t.keyCode===36)&&(typeof y.value!="string"||y.value.length===0)&&(oe(t),h.value=-1,Ie(t.keyCode===36?1:-1,e.multiple)),(t.keyCode===33||t.keyCode===34)&&le.value!==void 0&&(oe(t),h.value=Math.max(-1,Math.min(K.value,h.value+(t.keyCode===33?-1:1)*le.value.view)),Ie(t.keyCode===33?1:-1,e.multiple)),(t.keyCode===38||t.keyCode===40)&&(oe(t),Ie(t.keyCode===38?-1:1,e.multiple));const v=K.value;if((P===void 0||T<Date.now())&&(P=""),v>0&&e.useInput!==!0&&t.key!==void 0&&t.key.length===1&&t.altKey===!1&&t.ctrlKey===!1&&t.metaKey===!1&&(t.keyCode!==32||P.length!==0)){i.value!==!0&&we(t);const O=t.key.toLocaleLowerCase(),A=P.length===1&&P[0]===O;T=Date.now()+1500,A===!1&&(oe(t),P+=O);const H=new RegExp("^"+P.split("").map($e=>Dl.indexOf($e)!==-1?"\\"+$e:$e).join(".*"),"i");let _=h.value;if(A===!0||_<0||H.test($.value(e.options[_]))!==!0)do _=dt(_+1,-1,v-1);while(_!==h.value&&(ge.value(e.options[_])===!0||H.test($.value(e.options[_]))!==!0));h.value!==_&&ne(()=>{he(_),X(_),_>=0&&e.useInput===!0&&e.fillInput===!0&&Be($.value(e.options[_]),!0)});return}if(!(t.keyCode!==13&&(t.keyCode!==32||e.useInput===!0||P!=="")&&(t.keyCode!==9||n===!1))){if(t.keyCode!==9&&oe(t),h.value!==-1&&h.value<v){fe(e.options[h.value]);return}if(a===!0){const O=(A,H)=>{if(H){if(ht(H)!==!0)return}else H=e.newValueMode;if(Oe("",e.multiple!==!0,!0),A==null)return;(H==="toggle"?fe:Ye)(A,H==="add-unique"),e.multiple!==!0&&(p.value!==null&&p.value.focus(),ve())};if(e.onNewValue!==void 0?r("newValue",y.value,O):O(y.value),e.multiple!==!0)return}i.value===!0?be():l.innerLoading.value!==!0&&we()}}function lt(){return M===!0?te.value:N.value!==null&&N.value.contentEl!==null?N.value.contentEl:void 0}function zt(){return lt()}function Ft(){return e.hideSelected===!0?[]:c["selected-item"]!==void 0?de.value.map(t=>c["selected-item"](t)).slice():c.selected!==void 0?[].concat(c.selected()):e.useChips===!0?de.value.map((t,a)=>V(El,{key:"option-"+a,removable:l.editable.value===!0&&ge.value(t.opt)!==!0,dense:!0,textColor:e.color,tabindex:J.value,onRemove(){t.removeAtIndex(a)}},()=>V("span",{class:"ellipsis",[t.html===!0?"innerHTML":"textContent"]:$.value(t.opt)}))):[V("span",{[Se.value===!0?"innerHTML":"textContent"]:R.value})]}function ut(){if(B.value===!0)return c["no-option"]!==void 0?c["no-option"]({inputValue:y.value}):void 0;const t=c.option!==void 0?c.option:n=>V(Ol,{key:n.index,...n.itemProps},()=>V(ql,()=>V(Al,()=>V("span",{[n.html===!0?"innerHTML":"textContent"]:n.label}))));let a=ke("div",Te.value.map(t));return c["before-options"]!==void 0&&(a=c["before-options"]().concat(a)),kl(c["after-options"],a)}function Mt(t,a){const n=a===!0?{...Me.value,...l.splitAttrs.attributes.value}:void 0,v={ref:a===!0?p:void 0,key:"i_t",class:C.value,style:e.inputStyle,value:y.value!==void 0?y.value:"",type:"search",...n,id:a===!0?l.targetUid.value:void 0,maxlength:e.maxlength,autocomplete:e.autocomplete,"data-autofocus":t===!0||e.autofocus===!0||void 0,disabled:e.disable===!0,readonly:e.readonly===!0,...qt.value};return t!==!0&&M===!0&&(Array.isArray(v.class)===!0?v.class=[...v.class,"no-pointer-events"]:v.class+=" no-pointer-events"),V("input",v)}function nt(t){S!==null&&(clearTimeout(S),S=null),w!==null&&(clearTimeout(w),w=null),!(t&&t.target&&t.target.qComposing===!0)&&(Be(t.target.value||""),D=!0,L=y.value,l.focused.value!==!0&&(M!==!0||F.value===!0)&&l.focus(),e.onFilter!==void 0&&(S=setTimeout(()=>{S=null,ye(y.value)},e.inputDebounce)))}function Be(t,a){y.value!==t&&(y.value=t,a===!0||e.inputDebounce===0||e.inputDebounce==="0"?r("inputValue",t):w=setTimeout(()=>{w=null,r("inputValue",t)},e.inputDebounce))}function Oe(t,a,n){D=n!==!0,e.useInput===!0&&(Be(t,!0),(a===!0||n!==!0)&&(L=t),a!==!0&&ye(t))}function ye(t,a,n){if(e.onFilter===void 0||a!==!0&&l.focused.value!==!0)return;l.innerLoading.value===!0?r("filterAbort"):(l.innerLoading.value=!0,s.value=!0),t!==""&&e.multiple!==!0&&o.value.length!==0&&D!==!0&&t===$.value(o.value[0])&&(t="");const v=setTimeout(()=>{i.value===!0&&(i.value=!1)},10);f!==null&&clearTimeout(f),f=v,r("filter",t,(O,A)=>{(a===!0||l.focused.value===!0)&&f===v&&(clearTimeout(f),typeof O=="function"&&O(),s.value=!1,ne(()=>{l.innerLoading.value=!1,l.editable.value===!0&&(a===!0?i.value===!0&&ve():i.value===!0?Ne(!0):i.value=!0),typeof A=="function"&&ne(()=>{A(b)}),typeof n=="function"&&ne(()=>{n(b)})}))},()=>{l.focused.value===!0&&f===v&&(clearTimeout(f),l.innerLoading.value=!1,s.value=!1),i.value===!0&&(i.value=!1)})}function Tt(){return V(Pl,{ref:N,class:m.value,style:e.popupContentStyle,modelValue:i.value,fit:e.menuShrink!==!0,cover:e.optionsCover===!0&&B.value!==!0&&e.useInput!==!0,anchor:e.menuAnchor,self:e.menuSelf,offset:e.menuOffset,dark:q.value,noParentEvent:!0,noRefocus:!0,noFocus:!0,noRouteDismiss:e.popupNoRouteDismiss,square:He.value,transitionShow:e.transitionShow,transitionHide:e.transitionHide,transitionDuration:e.transitionDuration,separateClosePopup:!0,...ce.value,onScrollPassive:ue,onBeforeShow:at,onBeforeHide:pt,onShow:It},ut)}function pt(t){it(t),be()}function It(){se()}function Bt(t){Ve(t),p.value!==null&&p.value.focus(),F.value=!0,window.scrollTo(window.pageXOffset||window.scrollX||document.body.scrollLeft||0,0)}function _t(t){Ve(t),ne(()=>{F.value=!1})}function Et(){const t=[V(Bl,{class:`col-auto ${l.fieldClass.value}`,...g.value,for:l.targetUid.value,dark:q.value,square:!0,loading:s.value,itemAligned:!1,filled:!0,stackLabel:y.value.length!==0,...l.splitAttrs.listeners.value,onFocus:Bt,onBlur:_t},{...c,rawControl:()=>l.getControl(!0),before:void 0,after:void 0})];return i.value===!0&&t.push(V("div",{ref:te,class:m.value+" scroll",style:e.popupContentStyle,...ce.value,onClick:je,onScrollPassive:ue},ut())),V(xl,{ref:G,modelValue:z.value,position:e.useInput===!0?"top":void 0,transitionShow:W,transitionHide:e.transitionHide,transitionDuration:e.transitionDuration,noRouteDismiss:e.popupNoRouteDismiss,onBeforeShow:at,onBeforeHide:Pt,onHide:Rt,onShow:Ht},()=>V("div",{class:"q-select__dialog"+(q.value===!0?" q-select__dialog--dark q-dark":"")+(F.value===!0?" q-select__dialog--focused":"")},t))}function Pt(t){it(t),G.value!==null&&G.value.__updateRefocusTarget(l.rootRef.value.querySelector(".q-field__native > [tabindex]:last-child")),l.focused.value=!1}function Rt(t){ve(),l.focused.value===!1&&r("blur",t),Ce()}function Ht(){const t=document.activeElement;(t===null||t.id!==l.targetUid.value)&&p.value!==null&&p.value!==t&&p.value.focus(),se()}function be(){z.value!==!0&&(h.value=-1,i.value===!0&&(i.value=!1),l.focused.value===!1&&(f!==null&&(clearTimeout(f),f=null),l.innerLoading.value===!0&&(r("filterAbort"),l.innerLoading.value=!1,s.value=!1)))}function we(t){l.editable.value===!0&&(M===!0?(l.onControlFocusin(t),z.value=!0,ne(()=>{l.focus()})):l.focus(),e.onFilter!==void 0?ye(y.value):(B.value!==!0||c["no-option"]!==void 0)&&(i.value=!0))}function ve(){z.value=!1,be()}function Ce(){e.useInput===!0&&Oe(e.multiple!==!0&&e.fillInput===!0&&o.value.length!==0&&$.value(o.value[0])||"",!0,!0)}function Ne(t){let a=-1;if(t===!0){if(o.value.length!==0){const n=Z.value(o.value[0]);a=e.options.findIndex(v=>ze(Z.value(v),n))}j(a)}he(a)}function Lt(t,a){i.value===!0&&l.innerLoading.value===!1&&(j(-1,!0),ne(()=>{i.value===!0&&l.innerLoading.value===!1&&(t>a?j():Ne(!0))}))}function ot(){z.value===!1&&N.value!==null&&N.value.updatePosition()}function at(t){t!==void 0&&Ve(t),r("popupShow",t),l.hasPopupOpen=!0,l.onControlFocusin(t)}function it(t){t!==void 0&&Ve(t),r("popupHide",t),l.hasPopupOpen=!1,l.onControlFocusout(t)}function rt(){M=k.platform.is.mobile!==!0&&e.behavior!=="dialog"?!1:e.behavior!=="menu"&&(e.useInput===!0?c["no-option"]!==void 0||e.onFilter!==void 0||B.value===!1:!0),W=k.platform.is.ios===!0&&M===!0&&e.useInput===!0?"fade":e.transitionShow}return yl(rt),bl(ot),rt(),Ue(()=>{S!==null&&clearTimeout(S),w!==null&&clearTimeout(w)}),Object.assign(b,{showPopup:we,hidePopup:ve,removeAtIndex:Le,add:Ye,toggleOption:fe,getOptionIndex:()=>h.value,setOptionIndex:he,moveOptionSelection:Ie,filter:ye,updateMenuPosition:ot,updateInputValue:Oe,isOptionSelected:Ke,getEmittingOptionValue:Xe,isOptionDisabled:(...t)=>ge.value.apply(null,t)===!0,getOptionValue:(...t)=>Z.value.apply(null,t),getOptionLabel:(...t)=>$.value.apply(null,t)}),Object.assign(l,{innerValue:o,fieldClass:d(()=>`q-select q-field--auto-height q-select--with${e.useInput!==!0?"out":""}-input q-select--with${e.useChips!==!0?"out":""}-chips q-select--${e.multiple===!0?"multiple":"single"}`),inputRef:me,targetRef:p,hasValue:I,showPopup:we,floatingLabel:d(()=>e.hideSelected!==!0&&I.value===!0||typeof y.value=="number"||y.value.length!==0||ct(e.displayValue)),getControlChild:()=>{if(l.editable.value!==!1&&(z.value===!0||B.value!==!0||c["no-option"]!==void 0))return M===!0?Et():Tt();l.hasPopupOpen===!0&&(l.hasPopupOpen=!1)},controlEvents:{onFocusin(t){l.onControlFocusin(t)},onFocusout(t){l.onControlFocusout(t,()=>{Ce(),be()})},onClick(t){if(je(t),M!==!0&&i.value===!0){be(),p.value!==null&&p.value.focus();return}we(t)}},getControl:t=>{const a=Ft(),n=t===!0||z.value!==!0||M!==!0;if(e.useInput===!0)a.push(Mt(t,n));else if(l.editable.value===!0){const O=n===!0?Me.value:void 0;a.push(V("input",{ref:n===!0?p:void 0,key:"d_t",class:"q-select__focus-target",id:n===!0?l.targetUid.value:void 0,value:R.value,readonly:!0,"data-autofocus":t===!0||e.autofocus===!0||void 0,...O,onKeydown:tt,onKeyup:Ze,onKeypress:et})),n===!0&&typeof e.autocomplete=="string"&&e.autocomplete.length!==0&&a.push(V("input",{class:"q-select__autocomplete-input",autocomplete:e.autocomplete,tabindex:-1,onKeyup:Ge}))}if(U.value!==void 0&&e.disable!==!0&&pe.value.length!==0){const O=pe.value.map(A=>V("option",{value:A,selected:!0}));a.push(V("select",{class:"hidden",name:U.value,multiple:e.multiple},O))}const v=e.useInput===!0||n!==!0?void 0:l.splitAttrs.attributes.value;return V("div",{class:"q-field__native row items-center",...v,...l.splitAttrs.listeners.value},a)},getInnerAppend:()=>e.loading!==!0&&s.value!==!0&&e.hideDropdownIcon!==!0?[V(_e,{class:"q-select__dropdown-icon"+(i.value===!0?" rotate-180":""),name:qe.value})]:null}),bt(l)}});export{Wl as Q,Pl as a,El as b,Ll as c,Ql as d,Ee as r,gt as u};
