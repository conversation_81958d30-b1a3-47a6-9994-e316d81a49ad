import{C as P,r as p,c as h,w as b,b7 as x,h as y,t as C,b6 as Q}from"./index.bf55edc3.js";import{a as j}from"./QSelect.aab60e9f.js";import{u as k,c as M}from"./position-engine.8228ce32.js";var I=P({name:"QPopupProxy",props:{...k,breakpoint:{type:[String,Number],default:450}},emits:["show","hide"],setup(a,{slots:g,emit:c,attrs:v}){const{proxy:r}=C(),{$q:l}=r,n=p(!1),t=p(null),i=h(()=>parseInt(a.breakpoint,10)),{canShow:f}=M({showing:n});function u(){return l.screen.width<i.value||l.screen.height<i.value?"dialog":"menu"}const o=p(u()),m=h(()=>o.value==="menu"?{maxHeight:"99vh"}:{});b(()=>u(),e=>{n.value!==!0&&(o.value=e)});function d(e){n.value=!0,c("show",e)}function w(e){n.value=!1,o.value=u(),c("hide",e)}return Object.assign(r,{show(e){f(e)===!0&&t.value.show(e)},hide(e){t.value.hide(e)},toggle(e){t.value.toggle(e)}}),x(r,"currentComponent",()=>({type:o.value,ref:t.value})),()=>{const e={ref:t,...m.value,...v,onShow:d,onHide:w};let s;return o.value==="dialog"?s=Q:(s=j,Object.assign(e,{target:a.target,contextMenu:a.contextMenu,noParentEvent:!0,separateClosePopup:!0})),y(s,e,g.default)}}});export{I as Q};
