<template>
  <q-card class="q-mt-md">
    <q-card-section>
      <!-- Tabs -->
      <q-tabs
        v-model="tab"
        dense
        align="justify"
        class="text-h6"
        active-color="primary"
        indicator-color="primary"
      >
        <q-tab name="1" label="分析結果" />
        <q-tab name="2" label="組合統計結果" />
        <q-tab name="3" label="開獎結果" />
      </q-tabs>

      <!-- 預測結果 -->
      <q-card
        bordered
        class="ball-card full-width q-my-lg"
        v-if="predictResult.period"
      >
        <q-card-section>
          <div class="row q-gutter-y-md">
            <div class="col-12 col-sm-4 draw-title text-center">
              <div class="text-h6">預測開獎結果</div>
              <div class="text-period">第 {{ predictResult.period }} 期</div>
              <div class="text-draw-date">
                開獎日期：{{ predictResult.draw_date }}
              </div>
            </div>

            <div class="col-12 col-sm-6 self-center">
              <div class="row justify-center">
                <template
                  v-for="number in predictResult.draw_number_size"
                  :key="number"
                >
                  <div class="col-auto">
                    <div class="ball" :key="number">
                      {{ paddingZero(number) }}
                    </div>
                  </div>
                </template>
                <div class="col-auto" v-if="predictResult.special_number">
                  <div
                    class="ball special-number"
                    :key="predictResult.special_number"
                  >
                    {{ paddingZero(predictResult.special_number) }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- Panels -->
      <q-tab-panels v-model="tab">
        <!-- 分析結果 -->
        <q-tab-panel name="1">
          <!-- 篩選 -->
          <div class="row q-my-md q-gutter-sm items-center">
            <div class="col-sm-auto text-h6">篩選</div>
            <!-- 連續拖出次數 -->
            <div class="col-sm-auto">
              <q-select
                outlined
                dense
                v-model="accuracy"
                :options="accuracyOpts"
                map-options
                emit-value
              />
            </div>
            <!-- 篩選條件 -->
            <div class="col-sm-auto">
              <q-select
                outlined
                dense
                v-model="filterCondition"
                :options="filterConditionOpts"
                map-options
                emit-value
              />
            </div>

            <!-- 統計方式 -->
            <div class="col-sm-auto">
              <q-select
                outlined
                dense
                v-model="statisticType"
                :options="statisticTypeOpts"
                map-options
                emit-value
              />
            </div>
          </div>
          <div class="row q-my-md">
            <!-- 資料筆數 -->
            <div class="col-auto text-h6">
              共 {{ paginatedResults.totalCount }} 筆資料
            </div>
          </div>

          <!-- 出現次數 -->
          <div class="row q-my-sm">
            <label class="col text-h6 text-bold">預測結果</label>
          </div>
          <q-card
            flat
            bordered
            class="q-pa-sm q-mb-md text-subtitle1 appearance"
          >
            <div class="row q-col-gutter-sm">
              <div
                class="col-4 col-md-2"
                :class="{ predict: predictResult.tailSet?.includes(number) }"
                v-for="number in paginatedResults.targetNumAppearances.keys()"
                :key="number"
              >
                <div class="row items-center">
                  <span class="ball tail-number">{{ number }}</span>
                  ({{ paginatedResults.targetNumAppearances.get(number) }}次)
                </div>
              </div>
            </div>
          </q-card>

          <!-- 上方分頁 -->
          <div
            class="row q-my-md justify-center"
            v-if="paginatedResults.totalPages > 1"
          >
            <q-pagination
              v-model="currentPage"
              :max="paginatedResults.totalPages"
              :input="true"
            />
          </div>

          <!-- 資料顯示 -->
          <div class="row q-col-gutter-md">
            <!-- 無符合條件結果提示 -->
            <div
              class="col-12 q-my-sm"
              v-if="paginatedResults.pageItems.length === 0"
            >
              <q-card flat bordered>
                <q-card-section>
                  <div class="text-h6 text-center">沒有符合條件的結果</div>
                </q-card-section>
              </q-card>
            </div>

            <!-- 統計資料 -->
            <template
              v-for="(stat, index) in paginatedResults.pageItems"
              :key="index"
            >
              <div class="col-12 q-my-sm">
                <q-card flat bordered>
                  <q-card-section>
                    <div class="row q-col-gutter-md items-center text-h6">
                      <!-- 開出 -->
                      <div class="col-12 col-sm-4">
                        <div>
                          開出
                          <q-chip
                            v-for="number in stat.firstNumbers"
                            :key="number"
                            color="primary"
                            text-color="white"
                            size="lg"
                            dense
                          >
                            {{ number }}尾
                          </q-chip>
                          下{{ stat.gap }}期開
                          <q-chip
                            v-for="number in stat.secondNumbers"
                            :key="number"
                            color="secondary"
                            text-color="white"
                            size="lg"
                            dense
                          >
                            {{ number }}尾
                          </q-chip>
                          再下{{ stat.targetGap }}期預測拖出
                          <q-chip
                            v-for="number in stat.targetNumbers"
                            :key="number"
                            color="accent"
                            text-color="white"
                            size="lg"
                            dense
                          >
                            {{ number }}尾
                          </q-chip>
                        </div>
                      </div>
                      <!-- 數據 -->
                      <div class="col-12 col-sm-6">
                        <div class="row q-col-gutter-md">
                          <div class="col-auto">
                            已連續拖出{{ stat.consecutiveHits }}次
                          </div>
                        </div>
                      </div>
                      <!-- 檢視詳細 -->
                      <div class="col-12 col-sm-2 text-center">
                        <q-btn
                          dense
                          color="primary"
                          icon="visibility"
                          label="檢視詳情"
                          class="text-subtitle1 text-bold"
                          @click="$emit('view-detail', stat)"
                        />
                      </div>
                    </div>
                  </q-card-section>
                </q-card>
              </div>
            </template>
          </div>

          <!-- 下方分頁 -->
          <div
            class="row justify-center"
            v-if="paginatedResults.totalPages > 1"
          >
            <q-pagination
              v-model="currentPage"
              :max="paginatedResults.totalPages"
              :input="true"
            />
          </div>
        </q-tab-panel>

        <!-- 出現次數 -->
        <q-tab-panel name="2">
          <div class="row q-my-md">
            <span class="text-h6 q-mr-sm"> 尾數組合： </span>
            <q-select
              v-model="combineNumber"
              :options="combineOptions"
              map-options
              emit-value
              outlined
              dense
            />
          </div>

          <!-- 出現次數 -->
          <div class="row q-my-sm">
            <label class="col text-h6 text-bold">
              尾數組合出現次數 ({{ drawResults.length }}期)
            </label>
          </div>
          <q-card
            flat
            bordered
            class="q-pa-sm q-mb-md text-subtitle1 appearance"
          >
            <div class="row q-gutter-xs">
              <div
                class="col-4 col-md-2"
                :class="{ predict: isIncludePredictCombine(combine) }"
                v-for="combine in combineAppearances.keys()"
                :key="combine"
              >
                <div class="row items-center">
                  <span
                    class="ball tail-number"
                    v-for="number of combine.split(',')"
                    :key="number"
                    >{{ number }}</span
                  >
                  ({{ combineAppearances.get(combine) }}次)
                </div>
              </div>
            </div>
          </q-card>
        </q-tab-panel>

        <!-- 開獎結果 -->
        <q-tab-panel name="3">
          <q-table
            :rows="drawResults"
            :columns="columns"
            :rows-per-page-options="[0]"
            hide-bottom
            separator="cell"
            class="q-mt-lg"
          >
            <!-- Header -->
            <template v-slot:header="props">
              <q-tr
                :props="props"
                v-if="!isMobileView"
                class="bg-primary text-white"
              >
                <q-th v-for="col in props.cols" :key="col.name" :props="props">
                  {{ col.label }}
                </q-th>
              </q-tr>
            </template>

            <!-- Body -->
            <!-- Normal view -->
            <template v-if="!isMobileView" v-slot:body="props">
              <q-tr :props="props">
                <q-td key="period" :props="props">
                  <div class="text-h6">
                    {{ props.row.period }} <br />
                    {{ props.row.draw_date }}
                  </div>
                </q-td>
                <q-td key="draw_number_size" :props="props">
                  <div class="row justify-center">
                    <template
                      v-for="number in props.row.draw_number_size"
                      :key="number"
                    >
                      <div class="col-auto">
                        <div class="ball" :key="number">
                          {{ paddingZero(number) }}
                        </div>
                      </div>
                    </template>
                    <div class="col-auto" v-if="props.row.special_number">
                      <div
                        class="ball special-number"
                        :key="props.row.special_number"
                      >
                        {{ paddingZero(props.row.special_number) }}
                      </div>
                    </div>
                  </div>
                </q-td>
                <q-td key="tail1" :props="props">
                  <span
                    v-for="number in props.row.tails.get(1)"
                    :key="number"
                    class="text-h6"
                    :class="{
                      'text-negative':
                        number === props.row.special_number && !isSuperLotto,
                    }"
                  >
                    {{ paddingZero(number) }} <br />
                  </span>
                </q-td>
                <q-td key="tail2" :props="props">
                  <span
                    v-for="number in props.row.tails.get(2)"
                    :key="number"
                    class="text-h6"
                    :class="{
                      'text-negative':
                        number === props.row.special_number && !isSuperLotto,
                    }"
                  >
                    {{ paddingZero(number) }}<br />
                  </span>
                </q-td>
                <q-td key="tail3" :props="props">
                  <span
                    v-for="number in props.row.tails.get(3)"
                    :key="number"
                    class="text-h6"
                    :class="{
                      'text-negative':
                        number === props.row.special_number && !isSuperLotto,
                    }"
                  >
                    {{ paddingZero(number) }}<br />
                  </span>
                </q-td>
                <q-td key="tail4" :props="props">
                  <span
                    v-for="number in props.row.tails.get(4)"
                    :key="number"
                    class="text-h6"
                    :class="{
                      'text-negative':
                        number === props.row.special_number && !isSuperLotto,
                    }"
                  >
                    {{ paddingZero(number) }}<br />
                  </span>
                </q-td>
                <q-td key="tail5" :props="props">
                  <span
                    v-for="number in props.row.tails.get(5)"
                    :key="number"
                    class="text-h6"
                    :class="{
                      'text-negative':
                        number === props.row.special_number && !isSuperLotto,
                    }"
                  >
                    {{ paddingZero(number) }}<br />
                  </span>
                </q-td>
                <q-td key="tail6" :props="props">
                  <span
                    v-for="number in props.row.tails.get(6)"
                    :key="number"
                    class="text-h6"
                    :class="{
                      'text-negative':
                        number === props.row.special_number && !isSuperLotto,
                    }"
                  >
                    {{ paddingZero(number) }}<br />
                  </span>
                </q-td>
                <q-td key="tail7" :props="props">
                  <span
                    v-for="number in props.row.tails.get(7)"
                    :key="number"
                    class="text-h6"
                    :class="{
                      'text-negative':
                        number === props.row.special_number && !isSuperLotto,
                    }"
                  >
                    {{ paddingZero(number) }}<br />
                  </span>
                </q-td>
                <q-td key="tail8" :props="props">
                  <span
                    v-for="number in props.row.tails.get(8)"
                    :key="number"
                    class="text-h6"
                    :class="{
                      'text-negative':
                        number === props.row.special_number && !isSuperLotto,
                    }"
                  >
                    {{ paddingZero(number) }}<br />
                  </span>
                </q-td>
                <q-td key="tail9" :props="props">
                  <span
                    v-for="number in props.row.tails.get(9)"
                    :key="number"
                    class="text-h6"
                    :class="{
                      'text-negative':
                        number === props.row.special_number && !isSuperLotto,
                    }"
                  >
                    {{ paddingZero(number) }}<br />
                  </span>
                </q-td>
                <q-td key="tail0" :props="props">
                  <span
                    v-for="number in props.row.tails.get(0)"
                    :key="number"
                    class="text-h6"
                    :class="{
                      'text-negative':
                        number === props.row.special_number && !isSuperLotto,
                    }"
                  >
                    {{ paddingZero(number) }}<br />
                  </span>
                </q-td>
              </q-tr>
            </template>

            <!-- Mobile view uses card format -->
            <template v-else v-slot:body="props">
              <q-card square bordered :key="props.row.period">
                <q-card-section class="q-px-none q-py-sm">
                  <!-- 期號 | 開獎時間 -->
                  <div class="row items-center">
                    <div class="col-12 q-mb-sm text-h6 text-center">
                      <span> {{ props.row.period }}期 </span>
                      <span> | </span>
                      <span>
                        {{ props.row.drawDate }}
                      </span>
                    </div>
                    <!-- 開獎號碼 -->
                    <div class="col-12 col">
                      <div class="row justify-center">
                        <template
                          v-for="number in props.row.draw_number_size"
                          :key="number"
                        >
                          <div class="col-auto">
                            <div class="ball" :key="number">
                              {{ paddingZero(number) }}
                            </div>
                          </div>
                        </template>
                        <div class="col-auto" v-if="props.row.special_number">
                          <div
                            class="ball special-number"
                            :key="props.row.special_number"
                          >
                            {{ paddingZero(props.row.special_number) }}
                          </div>
                        </div>
                      </div>
                    </div>
                    <!-- 尾數 -->
                    <div class="col-12 q-mt-md">
                      <div class="row q-gutter-xs justify-center">
                        <div
                          class="col-1 text-center"
                          style="border: 1px solid black"
                          v-for="i in [1, 2, 3, 4, 5, 6, 7, 8, 9, 0]"
                          :key="i"
                        >
                          <div
                            class="text-subtitle1"
                            style="border-bottom: 1px solid black"
                          >
                            尾{{ i }}
                          </div>
                          <div>
                            <span
                              v-for="number in props.row.tails.get(i)"
                              :key="number"
                              class="text-h6"
                              :class="{
                                'text-negative':
                                  number === props.row.special_number &&
                                  !isSuperLotto,
                              }"
                            >
                              {{ paddingZero(number) }}<br />
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </q-card-section>
              </q-card>
            </template>
          </q-table>
        </q-tab-panel>
      </q-tab-panels>
    </q-card-section>
  </q-card>
</template>

<script lang="ts" setup>
import { ref, computed, watch, onMounted } from 'vue';
import { useQuasar } from 'quasar';
import { LottoItem } from '@/api/modules/lotto';
import { DrawResult, StatResult, Occurrence } from '@/models/types';
import { paddingZero } from '@/utils';

const props = defineProps<{
  isSuperLotto: boolean;
  drawResults: LottoItem[];
  predictResult: LottoItem;
  drawTailResults: DrawResult[];
  rdResults: StatResult[];
  occurrenceResults: Map<string, Occurrence>;
  pageSize: number;
}>();

const $q = useQuasar();
const isMobileView = !$q.platform.is.desktop;

const tab = ref('1');

defineEmits(['view-detail']);

// 分析結果頁數
const currentPage = ref(1);
const paginatedResults = ref({
  pageItems: [] as StatResult[],
  targetNumAppearances: new Map(),
  totalCount: 0,
  totalPages: 0,
});

// 出現次數統計
const combineNumber = ref(1);
const combineOptions = [
  { label: '一星組合', value: 1 },
  { label: '二星組合', value: 2 },
  { label: '三星組合', value: 3 },
  { label: '四星組合', value: 4 },
  { label: '五星組合', value: 5 },
];
const combineAppearances = ref<Map<string, number>>(new Map());

// 準確率
const accuracy = ref(1);
const maxAccuracy = ref(1);
const accuracyOpts = computed(() => {
  return Array.from({ length: maxAccuracy.value }, (_, i) => ({
    label: `已連續拖出 ${i + 1} 次`,
    value: i + 1,
  }));
});



const statisticType = ref('count');

// 篩選條件
const filterCondition = ref('above');
const filterConditionOpts = ref([
  { label: '(含)以上', value: 'above' },
  { label: '(含)以下', value: 'below' },
  { label: '剛好', value: 'exact' },
]);
const statisticTypeOpts = ref([
  { label: '準確次數統計', value: 'count' },
  { label: '預測組數統計', value: 'group' },
]);

// 開獎結果
const columns = [
  {
    name: 'period',
    label: '期數',
    field: 'period',
    align: 'center' as const,
  },
  {
    name: 'draw_number_size',
    label: '開獎號碼',
    field: 'draw_number_size',
    align: 'center' as const,
    format: (val: number[]) => val.join(' '),
  },
  {
    name: 'tail1',
    label: '尾1',
    field: 'tail1',
    align: 'center' as const,
  },
  {
    name: 'tail2',
    label: '尾2',
    field: 'tail2',
    align: 'center' as const,
  },
  {
    name: 'tail3',
    label: '尾3',
    field: 'tail3',
    align: 'center' as const,
  },
  {
    name: 'tail4',
    label: '尾4',
    field: 'tail4',
    align: 'center' as const,
  },
  {
    name: 'tail5',
    label: '尾5',
    field: 'tail5',
    align: 'center' as const,
  },
  {
    name: 'tail6',
    label: '尾6',
    field: 'tail6',
    align: 'center' as const,
  },
  {
    name: 'tail7',
    label: '尾7',
    field: 'tail7',
    align: 'center' as const,
  },
  {
    name: 'tail8',
    label: '尾8',
    field: 'tail8',
    align: 'center' as const,
  },
  {
    name: 'tail9',
    label: '尾9',
    field: 'tail9',
    align: 'center' as const,
  },
  {
    name: 'tail0',
    label: '尾0',
    field: 'tail0',
    align: 'center' as const,
  },
];

const isInit = ref(false);
onMounted(() => {
  isInit.value = false;
  init();

  filterResults();

  isInit.value = true;
});

watch(() => accuracy.value, () => {
  if (!isInit.value) return;

  filterResults();
});

watch(() => filterCondition.value, () => {
  if (!isInit.value) return;

  filterResults();
});



watch(() => statisticType.value, () => {
  if (!isInit.value) return;

  filterResults();
});

watch(() => currentPage.value, () => {
  if (!isInit.value) return;

  pageChange();
});

watch(() => combineNumber.value, () => {
  statTailCombine();
});

const statTailCombine = () => {
  const stat = new Map();

  for (const result of props.drawTailResults) {
    const groupsGen = getCombinationsGenerator(
      result.numbers,
      combineNumber.value
    );
    const groups = Array.from(groupsGen);

    for (const group of groups) {
      let key = '';
      for (let i = 0; i < group.length; i++) {
        key += `${group[i]}`;
        if (i < group.length - 1) {
          key += ', ';
        }
      }

      const appearance = stat.get(key);

      if (appearance) {
        stat.set(key, appearance + 1);
      } else {
        stat.set(key, 1);
      }
    }

    const sorted = Array.from(stat.entries()).sort((a, b) => b[1] - a[1]);

    combineAppearances.value = new Map(sorted);
  }
};

const isIncludePredictCombine = (combine: string) => {
  const groupsGen = getCombinationsGenerator(
    props.predictResult.tailSet || [],
    combineNumber.value
  );
  const groups = Array.from(groupsGen);
  const combines = [];

  for (const group of groups) {
    let key = '';
    for (let i = 0; i < group.length; i++) {
      key += `${group[i]}`;
      if (i < group.length - 1) {
        key += ', ';
      }
    }

    combines.push(key);
  }

  return combines.includes(combine);
};

function getCombinationsGenerator(
  numbers: number[],
  size: number
): Generator<number[]> {
  // 直接回傳 generator，不做全量緩存
  return generateCombinationsGenerator(numbers, size);
}

function* generateCombinationsGenerator(
  numbers: number[],
  choose: number,
  start = 0,
  current: number[] = []
): Generator<number[]> {
  if (current.length === choose) {
    yield [...current];
    return;
  }
  for (let i = start; i < numbers.length; i++) {
    current.push(numbers[i]);
    yield* generateCombinationsGenerator(numbers, choose, i + 1, current);
    current.pop();
  }
}

const init = () => {
  currentPage.value = 1;
  combineNumber.value = 1;
  statTailCombine();

  for (let result of props.drawResults) {
    result.tails = new Map<number, number[]>();

    for (let i = 0; i < 10; i++) {
      result.tails.set(i, []);
    }

    let numbers = [...result.draw_number_size];
    if (!props.isSuperLotto && result.special_number) {
      numbers.push(result.special_number);
      numbers = numbers.sort((a, b) => a - b);
    }

    for (const number of numbers) {
      const tail = number % 10;
      result.tails.get(tail)?.push(number);
    }
  }
};

// const getStatOccurrence = (stat: StatResult) => {
//   const key =
//     stat.firstNumbers.join(',') +
//     '-' +
//     stat.secondNumbers.join(',') +
//     '-' +
//     stat.gap +
//     '-' +
//     stat.targetGap;

//   return props.occurrenceResults.get(key)?.count || 0;
// };

const currentResults = ref<StatResult[]>([]);
const filterResults = () => {
  currentResults.value = [];
  currentPage.value = 1;

  const frequencyMap = new Map<number, number>();

  maxAccuracy.value = 1;
  for (const result of props.rdResults) {
    let isFilter = false;

    // 根據篩選條件判斷
    switch (filterCondition.value) {
      case 'above':
        isFilter = result.consecutiveHits >= accuracy.value;
        break;
      case 'below':
        isFilter = result.consecutiveHits <= accuracy.value;
        break;
      case 'exact':
        isFilter = result.consecutiveHits === accuracy.value;
        break;
      default:
        isFilter = result.consecutiveHits >= accuracy.value;
    }
    if (isFilter) {
      currentResults.value.push(result);

      if (result.consecutiveHits > maxAccuracy.value) {
        maxAccuracy.value = result.consecutiveHits;
      }

      if (statisticType.value === 'count') {
        for (const number of result.targetNumbers) {
          const count = result.consecutiveHits;
          frequencyMap.set(number, (frequencyMap.get(number) || 0) + count);
        }
      } else if (statisticType.value === 'group') {
        for (const number of result.targetNumbers) {
          frequencyMap.set(number, (frequencyMap.get(number) || 0) + 1);
        }
      }
    }

  }

  // 將 Map 轉換為陣列並依照出現次數排序（由大到小）
  const sortedEntries = Array.from(frequencyMap.entries()).sort(
    (a, b) => b[1] - a[1]
  );
  // 轉回 Map（如果你還需要以 Map 儲存）
  paginatedResults.value.targetNumAppearances = new Map(sortedEntries);

  pageChange();
};

const pageChange = () => {
  const start = (currentPage.value - 1) * props.pageSize;
  const end = start + props.pageSize;
  paginatedResults.value.pageItems = currentResults.value.slice(start, end);
  paginatedResults.value.totalCount = currentResults.value.length;
  paginatedResults.value.totalPages = Math.ceil(
    currentResults.value.length / props.pageSize
  );
};
</script>
