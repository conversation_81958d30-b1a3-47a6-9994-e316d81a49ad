import{Q as vt}from"./QSpinnerDots.e03dba06.js";import{C as ft,aq as ht,aR as q,U as gt,as as _t,r as P,c as C,aS as pt,au as yt,aT as bt,av as xt,aU as kt,w as R,a as H,ap as F,ak as M,W as Ct,h as N,T as Tt,t as Pt,Q as wt,ab as St,k as Ot,a0 as qt,b as Bt,I as u,J as j,L as b,P as e,az as h,M as x,aA as z,aC as I,a5 as Et,aB as V,a2 as Lt,a3 as At,O as k,A as W,a8 as Dt,ba as Qt,N as Rt}from"./index.3dd97541.js";import{e as Ht,v as $,a as Ft,p as U,b as Mt,c as Nt,r as J,s as jt,d as zt}from"./position-engine.bb1fccc0.js";import{c as Z}from"./selection.ebf7bbb8.js";import{Q as It}from"./QPage.8036a276.js";import{L as Vt}from"./lotto.42d4db2a.js";import{p as G}from"./padding.dd505b59.js";import{_ as Wt}from"./plugin-vue_export-helper.21dcd24c.js";var $t=ft({name:"QTooltip",inheritAttrs:!1,props:{...Ht,...ht,...q,maxHeight:{type:String,default:null},maxWidth:{type:String,default:null},transitionShow:{...q.transitionShow,default:"jump-down"},transitionHide:{...q.transitionHide,default:"jump-up"},anchor:{type:String,default:"bottom middle",validator:$},self:{type:String,default:"top middle",validator:$},offset:{type:Array,default:()=>[14,14],validator:Ft},scrollTarget:gt,delay:{type:Number,default:0},hideDelay:{type:Number,default:0},persistent:Boolean},emits:[..._t],setup(t,{slots:w,emit:d,attrs:a}){let m,v;const _=Pt(),{proxy:{$q:l}}=_,c=P(null),o=P(!1),i=C(()=>U(t.anchor,l.lang.rtl)),n=C(()=>U(t.self,l.lang.rtl)),g=C(()=>t.persistent!==!0),{registerTick:K,removeTick:X}=pt(),{registerTimeout:T}=yt(),{transitionProps:Y,transitionStyle:tt}=bt(t),{localScrollTarget:B,changeScrollEvent:et,unconfigureScrollTarget:ot}=Mt(t,D),{anchorEl:r,canShow:st,anchorEvents:p}=Nt({showing:o,configureAnchorEl:ct}),{show:at,hide:S}=xt({showing:o,canShow:st,handleShow:nt,handleHide:it,hideOnRouteChange:g,processOnMount:!0});Object.assign(p,{delayShow:rt,delayHide:ut});const{showPortal:E,hidePortal:L,renderPortal:lt}=kt(_,c,mt,"tooltip");if(l.platform.is.mobile===!0){const s={anchorEl:r,innerRef:c,onClickOutside(f){return S(f),f.target.classList.contains("q-dialog__backdrop")&&St(f),!0}},O=C(()=>t.modelValue===null&&t.persistent!==!0&&o.value===!0);R(O,f=>{(f===!0?zt:J)(s)}),H(()=>{J(s)})}function nt(s){E(),K(()=>{v=new MutationObserver(()=>y()),v.observe(c.value,{attributes:!1,childList:!0,characterData:!0,subtree:!0}),y(),D()}),m===void 0&&(m=R(()=>l.screen.width+"|"+l.screen.height+"|"+t.self+"|"+t.anchor+"|"+l.lang.rtl,y)),T(()=>{E(!0),d("show",s)},t.transitionDuration)}function it(s){X(),L(),A(),T(()=>{L(!0),d("hide",s)},t.transitionDuration)}function A(){v!==void 0&&(v.disconnect(),v=void 0),m!==void 0&&(m(),m=void 0),ot(),F(p,"tooltipTemp")}function y(){jt({targetEl:c.value,offset:t.offset,anchorEl:r.value,anchorOrigin:i.value,selfOrigin:n.value,maxHeight:t.maxHeight,maxWidth:t.maxWidth})}function rt(s){if(l.platform.is.mobile===!0){Z(),document.body.classList.add("non-selectable");const O=r.value,f=["touchmove","touchcancel","touchend","click"].map(Q=>[O,Q,"delayHide","passiveCapture"]);M(p,"tooltipTemp",f)}T(()=>{at(s)},t.delay)}function ut(s){l.platform.is.mobile===!0&&(F(p,"tooltipTemp"),Z(),setTimeout(()=>{document.body.classList.remove("non-selectable")},10)),T(()=>{S(s)},t.hideDelay)}function ct(){if(t.noParentEvent===!0||r.value===null)return;const s=l.platform.is.mobile===!0?[[r.value,"touchstart","delayShow","passive"]]:[[r.value,"mouseenter","delayShow","passive"],[r.value,"mouseleave","delayHide","passive"]];M(p,"anchor",s)}function D(){if(r.value!==null||t.scrollTarget!==void 0){B.value=Ct(r.value,t.scrollTarget);const s=t.noParentEvent===!0?y:S;et(B.value,s)}}function dt(){return o.value===!0?N("div",{...a,ref:c,class:["q-tooltip q-tooltip--style q-position-engine no-pointer-events",a.class],style:[a.style,tt.value],role:"tooltip"},wt(w.default)):null}function mt(){return N(Tt,Y.value,dt)}return H(A),Object.assign(_.proxy,{updatePosition:y}),lt}});const Ut={key:0,class:"text-center q-py-xl"},Jt={key:1,class:"results-container"},Zt={class:"row items-center"},Gt={class:"col-12 col-md-3 text-center q-mb-md"},Kt={class:"lotto-type-name text-weight-bold q-mb-md"},Xt={class:"period-info"},Yt={class:"period-number text-weight-bold text-black"},te={class:"draw-date text-grey-7"},ee={class:"col-12 col-md-8 q-mb-md"},oe={class:"numbers-section"},se={class:"row justify-center q-gutter-sm"},ae={class:"ball"},le={key:0,class:"col-auto"},ne={class:"ball special-number"},ie={class:"col-12 col-md-1 text-center"},re={key:2,class:"text-center q-py-xl"},ue=Ot({name:"LottoResultsPage",__name:"LottoResultsPage",setup(t){const w=qt(),d=P(!0),a=P({}),m=["super_lotto638","lotto649","daily539","lotto_hk"],v=C(()=>m.filter(o=>a.value[o])),_=o=>{switch(o){case"super_lotto638":return"\u5A01\u529B\u5F69";case"lotto649":return"\u5927\u6A02\u900F";case"daily539":return"\u4ECA\u5F69539";case"lotto_hk":return"\u516D\u5408\u5F69";default:return""}},l=async()=>{try{d.value=!0;const{data:o}=await Vt.getLatestResults();a.value=o}catch(o){console.error("\u7372\u53D6\u6700\u65B0\u958B\u734E\u7D50\u679C\u5931\u6557:",o)}finally{d.value=!1}},c=o=>{w.push(`/lotto-detail/${o}`)};return Bt(()=>{l()}),(o,i)=>(u(),j(It,{class:"justify-center q-pa-md"},{default:b(()=>[i[3]||(i[3]=e("div",{class:"text-center q-mb-lg"},[e("h4",{class:"text-h4 text-weight-bold q-my-md"},"\u6700\u65B0\u958B\u734E\u7D50\u679C")],-1)),d.value?(u(),h("div",Ut,[x(vt,{size:"50px",color:"primary"}),i[0]||(i[0]=e("div",{class:"text-h6 q-mt-md"},"\u8F09\u5165\u4E2D...",-1))])):(u(),h("div",Jt,[(u(!0),h(z,null,I(v.value,n=>(u(),j(Lt,{key:n,class:"lotto-result-card cursor-pointer q-mb-lg",onClick:g=>c(n)},{default:b(()=>[x(At,{class:"q-pa-xl"},{default:b(()=>[e("div",Zt,[e("div",Gt,[e("div",Kt,k(_(n)),1),e("div",Xt,[e("div",Yt," \u7B2C "+k(a.value[n].period)+" \u671F ",1),e("div",te,k(a.value[n].draw_date),1)])]),e("div",ee,[e("div",oe,[e("div",se,[(u(!0),h(z,null,I(a.value[n].draw_number_size,g=>(u(),h("div",{key:g,class:"col-auto"},[e("div",ae,k(W(G)(g)),1)]))),128)),a.value[n].special_number?(u(),h("div",le,[e("div",ne,k(W(G)(a.value[n].special_number)),1)])):V("",!0)])])]),e("div",ie,[x(Dt,{icon:"arrow_forward",color:"primary",round:"",size:"lg",class:"view-more-btn",onClick:Qt(g=>c(n),["stop"])},{default:b(()=>[x($t,null,{default:b(()=>i[1]||(i[1]=[Rt("\u67E5\u770B\u6B77\u53F2\u8A18\u9304")])),_:1})]),_:2},1032,["onClick"])])])]),_:2},1024)]),_:2},1032,["onClick"]))),128))])),!d.value&&Object.keys(a.value).length===0?(u(),h("div",re,[x(Et,{name:"info",size:"60px",color:"grey-5"}),i[2]||(i[2]=e("div",{class:"text-h6 q-mt-md text-grey-7"},"\u66AB\u7121\u958B\u734E\u8CC7\u6599",-1))])):V("",!0)]),_:1}))}});var pe=Wt(ue,[["__scopeId","data-v-31f31ec2"]]);export{pe as default};
