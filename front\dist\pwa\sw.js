"use strict";(()=>{try{self["workbox:core:7.0.0"]&&_()}catch{}var ie=(o,...e)=>{let t=o;return e.length>0&&(t+=` :: ${JSON.stringify(e)}`),t};var ee=ie;var c=class extends Error{constructor(e,t){let r=ee(e,t);super(r),this.name=e,this.details=t}};var F=new Set;var m={googleAnalytics:"googleAnalytics",precache:"precache-v2",prefix:"workbox",runtime:"runtime",suffix:typeof registration!="undefined"?registration.scope:""},K=o=>[m.prefix,o,m.suffix].filter(e=>e&&e.length>0).join("-"),ce=o=>{for(let e of Object.keys(m))o(e)},p={updateDetails:o=>{ce(e=>{typeof o[e]=="string"&&(m[e]=o[e])})},getGoogleAnalyticsName:o=>o||K(m.googleAnalytics),getPrecacheName:o=>o||K(m.precache),getPrefix:()=>m.prefix,getRuntimeName:o=>o||K(m.runtime),getSuffix:()=>m.suffix};function te(o,e){let t=new URL(o);for(let r of e)t.searchParams.delete(r);return t.href}async function M(o,e,t,r){let s=te(e.url,t);if(e.url===s)return o.match(e,r);let a=Object.assign(Object.assign({},r),{ignoreSearch:!0}),n=await o.keys(e,a);for(let i of n){let l=te(i.url,t);if(s===l)return o.match(i,r)}}var b;function I(){if(b===void 0){let o=new Response("");if("body"in o)try{new Response(o.body),b=!0}catch{b=!1}b=!1}return b}var v=class{constructor(){this.promise=new Promise((e,t)=>{this.resolve=e,this.reject=t})}};async function H(){for(let o of F)await o()}var j=o=>new URL(String(o),location.href).href.replace(new RegExp(`^${location.origin}`),"");function $(o){return new Promise(e=>setTimeout(e,o))}function O(o,e){let t=e();return o.waitUntil(t),t}async function B(o,e){let t=null;if(o.url&&(t=new URL(o.url).origin),t!==self.location.origin)throw new c("cross-origin-copy-response",{origin:t});let r=o.clone(),s={headers:new Headers(r.headers),status:r.status,statusText:r.statusText},a=e?e(s):s,n=I()?r.body:await r.blob();return new Response(n,a)}function G(){self.addEventListener("activate",()=>self.clients.claim())}try{self["workbox:precaching:7.0.0"]&&_()}catch{}var ue="__WB_REVISION__";function oe(o){if(!o)throw new c("add-to-cache-list-unexpected-type",{entry:o});if(typeof o=="string"){let a=new URL(o,location.href);return{cacheKey:a.href,url:a.href}}let{revision:e,url:t}=o;if(!t)throw new c("add-to-cache-list-unexpected-type",{entry:o});if(!e){let a=new URL(t,location.href);return{cacheKey:a.href,url:a.href}}let r=new URL(t,location.href),s=new URL(t,location.href);return r.searchParams.set(ue,e),{cacheKey:r.href,url:s.href}}var T=class{constructor(){this.updatedURLs=[],this.notUpdatedURLs=[],this.handlerWillStart=async({request:e,state:t})=>{t&&(t.originalRequest=e)},this.cachedResponseWillBeUsed=async({event:e,state:t,cachedResponse:r})=>{if(e.type==="install"&&t&&t.originalRequest&&t.originalRequest instanceof Request){let s=t.originalRequest.url;r?this.notUpdatedURLs.push(s):this.updatedURLs.push(s)}return r}}};var D=class{constructor({precacheController:e}){this.cacheKeyWillBeUsed=async({request:t,params:r})=>{let s=(r==null?void 0:r.cacheKey)||this._precacheController.getCacheKeyForURL(t.url);return s?new Request(s,{headers:t.headers}):t},this._precacheController=e}};try{self["workbox:strategies:7.0.0"]&&_()}catch{}function P(o){return typeof o=="string"?new Request(o):o}var q=class{constructor(e,t){this._cacheKeys={},Object.assign(this,t),this.event=t.event,this._strategy=e,this._handlerDeferred=new v,this._extendLifetimePromises=[],this._plugins=[...e.plugins],this._pluginStateMap=new Map;for(let r of this._plugins)this._pluginStateMap.set(r,{});this.event.waitUntil(this._handlerDeferred.promise)}async fetch(e){let{event:t}=this,r=P(e);if(r.mode==="navigate"&&t instanceof FetchEvent&&t.preloadResponse){let n=await t.preloadResponse;if(n)return n}let s=this.hasCallback("fetchDidFail")?r.clone():null;try{for(let n of this.iterateCallbacks("requestWillFetch"))r=await n({request:r.clone(),event:t})}catch(n){if(n instanceof Error)throw new c("plugin-error-request-will-fetch",{thrownErrorMessage:n.message})}let a=r.clone();try{let n;n=await fetch(r,r.mode==="navigate"?void 0:this._strategy.fetchOptions);for(let i of this.iterateCallbacks("fetchDidSucceed"))n=await i({event:t,request:a,response:n});return n}catch(n){throw s&&await this.runCallbacks("fetchDidFail",{error:n,event:t,originalRequest:s.clone(),request:a.clone()}),n}}async fetchAndCachePut(e){let t=await this.fetch(e),r=t.clone();return this.waitUntil(this.cachePut(e,r)),t}async cacheMatch(e){let t=P(e),r,{cacheName:s,matchOptions:a}=this._strategy,n=await this.getCacheKey(t,"read"),i=Object.assign(Object.assign({},a),{cacheName:s});r=await caches.match(n,i);for(let l of this.iterateCallbacks("cachedResponseWillBeUsed"))r=await l({cacheName:s,matchOptions:a,cachedResponse:r,request:n,event:this.event})||void 0;return r}async cachePut(e,t){let r=P(e);await $(0);let s=await this.getCacheKey(r,"write");if(!t)throw new c("cache-put-with-no-response",{url:j(s.url)});let a=await this._ensureResponseSafeToCache(t);if(!a)return!1;let{cacheName:n,matchOptions:i}=this._strategy,l=await self.caches.open(n),y=this.hasCallback("cacheDidUpdate"),g=y?await M(l,s.clone(),["__WB_REVISION__"],i):null;try{await l.put(s,y?a.clone():a)}catch(d){if(d instanceof Error)throw d.name==="QuotaExceededError"&&await H(),d}for(let d of this.iterateCallbacks("cacheDidUpdate"))await d({cacheName:n,oldResponse:g,newResponse:a.clone(),request:s,event:this.event});return!0}async getCacheKey(e,t){let r=`${e.url} | ${t}`;if(!this._cacheKeys[r]){let s=e;for(let a of this.iterateCallbacks("cacheKeyWillBeUsed"))s=P(await a({mode:t,request:s,event:this.event,params:this.params}));this._cacheKeys[r]=s}return this._cacheKeys[r]}hasCallback(e){for(let t of this._strategy.plugins)if(e in t)return!0;return!1}async runCallbacks(e,t){for(let r of this.iterateCallbacks(e))await r(t)}*iterateCallbacks(e){for(let t of this._strategy.plugins)if(typeof t[e]=="function"){let r=this._pluginStateMap.get(t);yield a=>{let n=Object.assign(Object.assign({},a),{state:r});return t[e](n)}}}waitUntil(e){return this._extendLifetimePromises.push(e),e}async doneWaiting(){let e;for(;e=this._extendLifetimePromises.shift();)await e}destroy(){this._handlerDeferred.resolve(null)}async _ensureResponseSafeToCache(e){let t=e,r=!1;for(let s of this.iterateCallbacks("cacheWillUpdate"))if(t=await s({request:this.request,response:t,event:this.event})||void 0,r=!0,!t)break;return r||t&&t.status!==200&&(t=void 0),t}};var W=class{constructor(e={}){this.cacheName=p.getRuntimeName(e.cacheName),this.plugins=e.plugins||[],this.fetchOptions=e.fetchOptions,this.matchOptions=e.matchOptions}handle(e){let[t]=this.handleAll(e);return t}handleAll(e){e instanceof FetchEvent&&(e={event:e,request:e.request});let t=e.event,r=typeof e.request=="string"?new Request(e.request):e.request,s="params"in e?e.params:void 0,a=new q(this,{event:t,request:r,params:s}),n=this._getResponse(a,r,t),i=this._awaitComplete(n,a,r,t);return[n,i]}async _getResponse(e,t,r){await e.runCallbacks("handlerWillStart",{event:r,request:t});let s;try{if(s=await this._handle(t,e),!s||s.type==="error")throw new c("no-response",{url:t.url})}catch(a){if(a instanceof Error){for(let n of e.iterateCallbacks("handlerDidError"))if(s=await n({error:a,event:r,request:t}),s)break}if(!s)throw a}for(let a of e.iterateCallbacks("handlerWillRespond"))s=await a({event:r,request:t,response:s});return s}async _awaitComplete(e,t,r,s){let a,n;try{a=await e}catch{}try{await t.runCallbacks("handlerDidRespond",{event:s,request:r,response:a}),await t.doneWaiting()}catch(i){i instanceof Error&&(n=i)}if(await t.runCallbacks("handlerDidComplete",{event:s,request:r,response:a,error:n}),t.destroy(),n)throw n}};var h=class extends W{constructor(e={}){e.cacheName=p.getPrecacheName(e.cacheName),super(e),this._fallbackToNetwork=e.fallbackToNetwork!==!1,this.plugins.push(h.copyRedirectedCacheableResponsesPlugin)}async _handle(e,t){let r=await t.cacheMatch(e);return r||(t.event&&t.event.type==="install"?await this._handleInstall(e,t):await this._handleFetch(e,t))}async _handleFetch(e,t){let r,s=t.params||{};if(this._fallbackToNetwork){let a=s.integrity,n=e.integrity,i=!n||n===a;if(r=await t.fetch(new Request(e,{integrity:e.mode!=="no-cors"?n||a:void 0})),a&&i&&e.mode!=="no-cors"){this._useDefaultCacheabilityPluginIfNeeded();let l=await t.cachePut(e,r.clone())}}else throw new c("missing-precache-entry",{cacheName:this.cacheName,url:e.url});return r}async _handleInstall(e,t){this._useDefaultCacheabilityPluginIfNeeded();let r=await t.fetch(e);if(!await t.cachePut(e,r.clone()))throw new c("bad-precaching-response",{url:e.url,status:r.status});return r}_useDefaultCacheabilityPluginIfNeeded(){let e=null,t=0;for(let[r,s]of this.plugins.entries())s!==h.copyRedirectedCacheableResponsesPlugin&&(s===h.defaultPrecacheCacheabilityPlugin&&(e=r),s.cacheWillUpdate&&t++);t===0?this.plugins.push(h.defaultPrecacheCacheabilityPlugin):t>1&&e!==null&&this.plugins.splice(e,1)}};h.defaultPrecacheCacheabilityPlugin={async cacheWillUpdate({response:o}){return!o||o.status>=400?null:o}};h.copyRedirectedCacheableResponsesPlugin={async cacheWillUpdate({response:o}){return o.redirected?await B(o):o}};var N=class{constructor({cacheName:e,plugins:t=[],fallbackToNetwork:r=!0}={}){this._urlsToCacheKeys=new Map,this._urlsToCacheModes=new Map,this._cacheKeysToIntegrities=new Map,this._strategy=new h({cacheName:p.getPrecacheName(e),plugins:[...t,new D({precacheController:this})],fallbackToNetwork:r}),this.install=this.install.bind(this),this.activate=this.activate.bind(this)}get strategy(){return this._strategy}precache(e){this.addToCacheList(e),this._installAndActiveListenersAdded||(self.addEventListener("install",this.install),self.addEventListener("activate",this.activate),this._installAndActiveListenersAdded=!0)}addToCacheList(e){let t=[];for(let r of e){typeof r=="string"?t.push(r):r&&r.revision===void 0&&t.push(r.url);let{cacheKey:s,url:a}=oe(r),n=typeof r!="string"&&r.revision?"reload":"default";if(this._urlsToCacheKeys.has(a)&&this._urlsToCacheKeys.get(a)!==s)throw new c("add-to-cache-list-conflicting-entries",{firstEntry:this._urlsToCacheKeys.get(a),secondEntry:s});if(typeof r!="string"&&r.integrity){if(this._cacheKeysToIntegrities.has(s)&&this._cacheKeysToIntegrities.get(s)!==r.integrity)throw new c("add-to-cache-list-conflicting-integrities",{url:a});this._cacheKeysToIntegrities.set(s,r.integrity)}if(this._urlsToCacheKeys.set(a,s),this._urlsToCacheModes.set(a,n),t.length>0){let i=`Workbox is precaching URLs without revision info: ${t.join(", ")}
This is generally NOT safe. Learn more at https://bit.ly/wb-precache`;console.warn(i)}}}install(e){return O(e,async()=>{let t=new T;this.strategy.plugins.push(t);for(let[a,n]of this._urlsToCacheKeys){let i=this._cacheKeysToIntegrities.get(n),l=this._urlsToCacheModes.get(a),y=new Request(a,{integrity:i,cache:l,credentials:"same-origin"});await Promise.all(this.strategy.handleAll({params:{cacheKey:n},request:y,event:e}))}let{updatedURLs:r,notUpdatedURLs:s}=t;return{updatedURLs:r,notUpdatedURLs:s}})}activate(e){return O(e,async()=>{let t=await self.caches.open(this.strategy.cacheName),r=await t.keys(),s=new Set(this._urlsToCacheKeys.values()),a=[];for(let n of r)s.has(n.url)||(await t.delete(n),a.push(n.url));return{deletedURLs:a}})}getURLsToCacheKeys(){return this._urlsToCacheKeys}getCachedURLs(){return[...this._urlsToCacheKeys.keys()]}getCacheKeyForURL(e){let t=new URL(e,location.href);return this._urlsToCacheKeys.get(t.href)}getIntegrityForCacheKey(e){return this._cacheKeysToIntegrities.get(e)}async matchPrecache(e){let t=e instanceof Request?e.url:e,r=this.getCacheKeyForURL(t);if(r)return(await self.caches.open(this.strategy.cacheName)).match(r)}createHandlerBoundToURL(e){let t=this.getCacheKeyForURL(e);if(!t)throw new c("non-precached-url",{url:e});return r=>(r.request=new Request(e),r.params=Object.assign({cacheKey:t},r.params),this.strategy.handle(r))}};var Q,f=()=>(Q||(Q=new N),Q);try{self["workbox:routing:7.0.0"]&&_()}catch{}var A="GET";var w=o=>o&&typeof o=="object"?o:{handle:o};var u=class{constructor(e,t,r=A){this.handler=w(t),this.match=e,this.method=r}setCatchHandler(e){this.catchHandler=w(e)}};var C=class extends u{constructor(e,t,r){let s=({url:a})=>{let n=e.exec(a.href);if(!!n&&!(a.origin!==location.origin&&n.index!==0))return n.slice(1)};super(s,t,r)}};var x=class{constructor(){this._routes=new Map,this._defaultHandlerMap=new Map}get routes(){return this._routes}addFetchListener(){self.addEventListener("fetch",e=>{let{request:t}=e,r=this.handleRequest({request:t,event:e});r&&e.respondWith(r)})}addCacheListener(){self.addEventListener("message",e=>{if(e.data&&e.data.type==="CACHE_URLS"){let{payload:t}=e.data,r=Promise.all(t.urlsToCache.map(s=>{typeof s=="string"&&(s=[s]);let a=new Request(...s);return this.handleRequest({request:a,event:e})}));e.waitUntil(r),e.ports&&e.ports[0]&&r.then(()=>e.ports[0].postMessage(!0))}})}handleRequest({request:e,event:t}){let r=new URL(e.url,location.href);if(!r.protocol.startsWith("http"))return;let s=r.origin===location.origin,{params:a,route:n}=this.findMatchingRoute({event:t,request:e,sameOrigin:s,url:r}),i=n&&n.handler,l=[],y=e.method;if(!i&&this._defaultHandlerMap.has(y)&&(i=this._defaultHandlerMap.get(y)),!i)return;let g;try{g=i.handle({url:r,request:e,event:t,params:a})}catch(E){g=Promise.reject(E)}let d=n&&n.catchHandler;return g instanceof Promise&&(this._catchHandler||d)&&(g=g.catch(async E=>{if(d)try{return await d.handle({url:r,request:e,event:t,params:a})}catch(Z){Z instanceof Error&&(E=Z)}if(this._catchHandler)return this._catchHandler.handle({url:r,request:e,event:t});throw E})),g}findMatchingRoute({url:e,sameOrigin:t,request:r,event:s}){let a=this._routes.get(r.method)||[];for(let n of a){let i,l=n.match({url:e,sameOrigin:t,request:r,event:s});if(l)return i=l,(Array.isArray(i)&&i.length===0||l.constructor===Object&&Object.keys(l).length===0||typeof l=="boolean")&&(i=void 0),{route:n,params:i}}return{}}setDefaultHandler(e,t=A){this._defaultHandlerMap.set(t,w(e))}setCatchHandler(e){this._catchHandler=w(e)}registerRoute(e){this._routes.has(e.method)||this._routes.set(e.method,[]),this._routes.get(e.method).push(e)}unregisterRoute(e){if(!this._routes.has(e.method))throw new c("unregister-route-but-not-found-with-method",{method:e.method});let t=this._routes.get(e.method).indexOf(e);if(t>-1)this._routes.get(e.method).splice(t,1);else throw new c("unregister-route-route-not-registered")}};var k,S=()=>(k||(k=new x,k.addFetchListener(),k.addCacheListener()),k);function R(o,e,t){let r;if(typeof o=="string"){let a=new URL(o,location.href),n=({url:i})=>i.href===a.href;r=new u(n,e,t)}else if(o instanceof RegExp)r=new C(o,e,t);else if(typeof o=="function")r=new u(o,e,t);else if(o instanceof u)r=o;else throw new c("unsupported-route-type",{moduleName:"workbox-routing",funcName:"registerRoute",paramName:"capture"});return S().registerRoute(r),r}function se(o,e=[]){for(let t of[...o.searchParams.keys()])e.some(r=>r.test(t))&&o.searchParams.delete(t);return o}function*ae(o,{ignoreURLParametersMatching:e=[/^utm_/,/^fbclid$/],directoryIndex:t="index.html",cleanURLs:r=!0,urlManipulation:s}={}){let a=new URL(o,location.href);a.hash="",yield a.href;let n=se(a,e);if(yield n.href,t&&n.pathname.endsWith("/")){let i=new URL(n.href);i.pathname+=t,yield i.href}if(r){let i=new URL(n.href);i.pathname+=".html",yield i.href}if(s){let i=s({url:a});for(let l of i)yield l.href}}var U=class extends u{constructor(e,t){let r=({request:s})=>{let a=e.getURLsToCacheKeys();for(let n of ae(s.url,t)){let i=a.get(n);if(i){let l=e.getIntegrityForCacheKey(i);return{cacheKey:i,integrity:l}}}};super(r,e.strategy)}};function Y(o){let e=f(),t=new U(e,o);R(t)}var he="-precache-",ne=async(o,e=he)=>{let r=(await self.caches.keys()).filter(s=>s.includes(e)&&s.includes(self.registration.scope)&&s!==o);return await Promise.all(r.map(s=>self.caches.delete(s))),r};function V(){self.addEventListener("activate",o=>{let e=p.getPrecacheName();o.waitUntil(ne(e).then(t=>{}))})}function J(o){return f().createHandlerBoundToURL(o)}function z(o){f().precache(o)}function X(o,e){z(o),Y(e)}var L=class extends u{constructor(e,{allowlist:t=[/./],denylist:r=[]}={}){super(s=>this._match(s),e),this._allowlist=t,this._denylist=r}_match({url:e,request:t}){if(t&&t.mode!=="navigate")return!1;let r=e.pathname+e.search;for(let s of this._denylist)if(s.test(r))return!1;return!!this._allowlist.some(s=>s.test(r))}};self.skipWaiting();G();self.addEventListener("message",o=>{o.data&&o.data.type==="SKIP_WAITING"&&self.skipWaiting(),o.data&&o.data.type==="RELOAD_PAGE"&&self.clients.matchAll().then(e=>{e.forEach(t=>{t.postMessage({type:"RELOAD_PAGE"})})})});self.addEventListener("install",()=>{console.log("Service Worker \u6B63\u5728\u5B89\u88DD\u65B0\u7248\u672C"),self.skipWaiting()});self.addEventListener("activate",o=>{console.log("Service Worker \u5DF2\u555F\u52D5\uFF0C\u6B63\u5728\u63A5\u7BA1\u6240\u6709\u5BA2\u6236\u7AEF"),o.waitUntil(Promise.all([V(),self.clients.claim(),self.clients.matchAll().then(e=>{e.forEach(t=>{t.postMessage({type:"FORCE_RELOAD"})})})]))});X([{"revision":"bdb06484be0443ff998e0bad5079503f","url":"assets/analyzer.worker.2045078f.js"},{"revision":"76c985c11d9cc9c0f54ce8f7ec22ff46","url":"assets/BallFollowPage.0e438e71.css"},{"revision":"cc0c676c5f2591420c1b07cb6b5a365c","url":"assets/BallFollowPage.664f35ca.js"},{"revision":"9655f68e6ca2b4daae064e981f898c4a","url":"assets/BatchAnalysisPage.1badcd2e.css"},{"revision":"386025c88ecb669d506ed96b61ff9622","url":"assets/BatchAnalysisPage.90b4c3db.js"},{"revision":"0ed637601b1434d12b973a00f32ec63a","url":"assets/dialog.d8cf3b2f.js"},{"revision":"4381c2ab475346f8dfcb2eba1ed655f4","url":"assets/DisclaimerPage.0701b0f4.css"},{"revision":"0e1476fc2da09402d058c4fb4db6dcb6","url":"assets/DisclaimerPage.749139f9.js"},{"revision":"2879ced991f03c85d6f86411e6aaf33b","url":"assets/error-handler.a345d664.js"},{"revision":"f581d9e2a7852b10d212ff133b3bc1c9","url":"assets/ErrorNotFound.685d0aee.js"},{"revision":"3e1afe59fa075c9e04c436606b77f640","url":"assets/flUhRq6tzZclQEJ-Vdg-IuiaDsNa.fd84f88b.woff"},{"revision":"a4160421d2605545f69a4cd6cd642902","url":"assets/flUhRq6tzZclQEJ-Vdg-IuiaDsNcIhQ8tQ.4a4dbc62.woff2"},{"revision":"1f529bd239145d5581d497f91509c0ef","url":"assets/index.3dd97541.js"},{"revision":"69c4ced375f350371d6b749c4da2ea7e","url":"assets/index.6b749f6b.css"},{"revision":"a483de96a46e3f091d0eeb742d3e6046","url":"assets/IndexPage.8562f384.css"},{"revision":"e270a05d640fbec8c02e8b7b90bd3909","url":"assets/IndexPage.cdd7610e.js"},{"revision":"dfd89dcac6786e7d8a8eb90bd47365d9","url":"assets/InstallmentPage.95612164.js"},{"revision":"4aa2e69855e3b83110a251c47fdd05fc","url":"assets/KFOkCnqEu92Fr1MmgVxIIzQ.34e9582c.woff"},{"revision":"40bcb2b8cc5ed94c4c21d06128e0e532","url":"assets/KFOlCnqEu92Fr1MmEU9fBBc-.9ce7f3ac.woff"},{"revision":"ea60988be8d6faebb4bc2a55b1f76e22","url":"assets/KFOlCnqEu92Fr1MmSU5fBBc-.bf14c7d7.woff"},{"revision":"0774a8b7ca338dc1aba5a0ec8f2b9454","url":"assets/KFOlCnqEu92Fr1MmWUlfBBc-.e0fd57c0.woff"},{"revision":"bcb7c7e2499a055f0e2f93203bdb282b","url":"assets/KFOlCnqEu92Fr1MmYUtfBBc-.f6537e32.woff"},{"revision":"d3907d0ccd03b1134c24d3bcaf05b698","url":"assets/KFOmCnqEu92Fr1Mu4mxM.f2abf7fb.woff"},{"revision":"12376c7f117814d7fcc26f6605903ae5","url":"assets/LoginLayout.349bc137.css"},{"revision":"20f7c2c178205821df42a221217ff1a1","url":"assets/LoginLayout.c6d2008f.js"},{"revision":"89dafd8ff414936a787fe1e51ce3d0c8","url":"assets/LoginPage.1d06bcaf.js"},{"revision":"0f0ced8b896cbceb351f2916b86f5fda","url":"assets/lotto.42d4db2a.js"},{"revision":"d2c16fb7039e22eeb8b6f0d856519b78","url":"assets/LottoDetailPage.7d68b9a1.js"},{"revision":"0c7a8734490972c4d6c8f20794f66cdf","url":"assets/LottoDetailPage.a907d4c4.css"},{"revision":"268ac357ec4c6e64f92b7964dc714588","url":"assets/LottoResultsPage.4eb7443b.css"},{"revision":"170ef7a1e4cebeb5ed310f66db86c9f8","url":"assets/LottoResultsPage.cb85ab06.js"},{"revision":"1d84d99322950496a016099844ff1858","url":"assets/MainLayout.24bcd2fd.js"},{"revision":"4f31591fe0a274c928df67a4eb0bbc44","url":"assets/MainLayout.32683a3e.css"},{"revision":"dedebcba792758e15428b4d7f490a8cd","url":"assets/padding.dd505b59.js"},{"revision":"aa98e4f6c29718e6f5298cb483184d08","url":"assets/PatternPage.4f086bf6.js"},{"revision":"60ba86325ebcdaf712766c508437b54d","url":"assets/PatternPage.7ebd9c38.css"},{"revision":"b29b145139fc88e89a46af507277557d","url":"assets/plugin-vue_export-helper.21dcd24c.js"},{"revision":"f045ce300e648406db4650d917b8de29","url":"assets/position-engine.bb1fccc0.js"},{"revision":"34363daa2ba0a1e92ecda97385da1e12","url":"assets/ProfilePage.14b39986.css"},{"revision":"428fa4c5f4d30ca31aaae9251325a74d","url":"assets/ProfilePage.277a9497.js"},{"revision":"7c1fb72a102d1b33de7d0714a1f88793","url":"assets/QForm.c80c2d0f.js"},{"revision":"6b54804f124d61c31b0e3cfad49cc418","url":"assets/QItem.87fe5c76.js"},{"revision":"91720c6fa6559590ac75d083c7c17d1d","url":"assets/QLinearProgress.fa832131.js"},{"revision":"f3d1cf07df5b7b2139fe9f47425cfe80","url":"assets/QList.e8f9ac34.js"},{"revision":"d8a7970f616d218b5e56e2333555dceb","url":"assets/QPage.8036a276.js"},{"revision":"82b9610c9e6c0f6a26418b98d99307bb","url":"assets/QPagination.08d84e70.js"},{"revision":"1f4ee7a088d0a1be20135fc66ec54d57","url":"assets/QPopupProxy.33948bdc.js"},{"revision":"7963002a7b7e13487acd36530fb4f81f","url":"assets/QResizeObserver.f0438521.js"},{"revision":"794bd62b51eec5eb49c11563e4c22f6c","url":"assets/QSelect.1285d7d3.js"},{"revision":"a8656ae6ee48f5db1c4057845204aafc","url":"assets/QSpace.c6b54235.js"},{"revision":"fa239285f14f2f3413e1751302ccac84","url":"assets/QSpinnerDots.e03dba06.js"},{"revision":"4ed02f58e694b9949c58bc1047ed0578","url":"assets/QTable.4ae3294d.js"},{"revision":"94cb36e03bb6ff2406b678da9e71ca1c","url":"assets/QTabPanels.7809bfe9.js"},{"revision":"4559a23dadcbaeba05af8966be52d0e0","url":"assets/quasar.client.8135185d.js"},{"revision":"d4599a14ea95a9ad6aa149301c9e7f15","url":"assets/RegisterPage.123fe470.js"},{"revision":"24acd03b8cdeadcefd7e6a1b30fa2d43","url":"assets/selection.ebf7bbb8.js"},{"revision":"9c65d18e0dc3e90dcd1ffd8fdc44f2ed","url":"assets/TailPage.514137fe.css"},{"revision":"b4f9030d90e24a9c5dc9aca450fed8db","url":"assets/TailPage.ae025267.js"},{"revision":"18bb79344d803a590f7d9865661d3547","url":"assets/title.ed830fce.js"},{"revision":"88ce3843cbd234458fc111496fd90393","url":"assets/touch.9135741d.js"},{"revision":"a6c03a0899588d67ef6341883bf5080f","url":"assets/UpdateTestPage.934673f7.js"},{"revision":"e9c8057f34a84226d21388b537c670ea","url":"assets/use-quasar.0e42f40d.js"},{"revision":"38cc2d5d6134dee46cbc1c43bd1add15","url":"assets/use-render-cache.3aae9b27.js"},{"revision":"bccc999f0076a1f41e00e268f9515b89","url":"assets/useLotteryAnalysis.c47ac7e8.js"},{"revision":"d3b4d80ebc5c13917042c7754a0eb8c1","url":"assets/UserPage.82259ac9.css"},{"revision":"f801a0d03671a6471a267152b9d6682c","url":"assets/UserPage.b86dbcc0.js"},{"revision":"b2076471be81e4caa50a9d02d06c2f23","url":"favicon.ico"},{"revision":"f19d9df9cd22eeb5e813162612815e01","url":"icons/android-chrome-192x192.png"},{"revision":"98559fc32fe318837cc3e9daa139a914","url":"icons/android-chrome-512x512.png"},{"revision":"f19d9df9cd22eeb5e813162612815e01","url":"icons/android-chrome-maskable-192x192.png"},{"revision":"98559fc32fe318837cc3e9daa139a914","url":"icons/android-chrome-maskable-512x512.png"},{"revision":"f7b2a86e3ffb2573c7ad86f479cc5ab5","url":"icons/apple-touch-icon-120x120.png"},{"revision":"6ed868d6cd5b46a8275d36e675e17715","url":"icons/apple-touch-icon-152x152.png"},{"revision":"8a84e7019e94282e9936ec7bb0e6f16d","url":"icons/apple-touch-icon-180x180.png"},{"revision":"fa3b1a31033ec0a2e38057904441eae2","url":"icons/apple-touch-icon-60x60.png"},{"revision":"9270822328019363999bae708ec5a2b3","url":"icons/apple-touch-icon-76x76.png"},{"revision":"8a84e7019e94282e9936ec7bb0e6f16d","url":"icons/apple-touch-icon.png"},{"revision":"e46c484037b436bc1a68f0ba4f178527","url":"icons/favicon-16x16.png"},{"revision":"8a47389f2db9944dd97afdcc06bd1b90","url":"icons/favicon-32x32.png"},{"revision":"649c69287dcc70035474b00c215d0412","url":"icons/msapplication-icon-144x144.png"},{"revision":"6c2e97a70419d2145bf3e961c0984902","url":"icons/mstile-150x150.png"},{"revision":"56e9e3192c5d477824830df625d7e38f","url":"icons/safari-pinned-tab.svg"},{"revision":"922ade6884e995d409b267e59a1e1a4b","url":"images/install-1.jpg"},{"revision":"e691c097cd2042ab86cf980b44aeaead","url":"images/install-2.jpg"},{"revision":"112682719925efe4fad1fac6085a30b1","url":"images/install-3.jpg"},{"revision":"e2ec368f31154e3e5a1da4df157fc7c8","url":"images/safari.png"},{"revision":"e002278181e87361c0bdd7ffc4aee2b8","url":"index.html"},{"revision":"12889017af3baa75c6b7dd4dce204338","url":"manifest.json"},{"revision":"61b8161b417bcc4ce1ac86920675e27a","url":"version.json"}]);V();try{let o="index.html";console.log("Service Worker: \u8A2D\u7F6E\u5C0E\u822A\u56DE\u9000 URL:",o),R(new L(J(o),{denylist:[/sw\.js$/,/workbox-(.)*\.js$/]}))}catch(o){console.error("Service Worker: \u8A2D\u7F6E\u5C0E\u822A\u56DE\u9000\u5931\u6557:",o),R(({request:e})=>e.mode==="navigate",({url:e})=>(console.log("Service Worker: \u8655\u7406\u5C0E\u822A\u8ACB\u6C42:",e.pathname),fetch("/index.html").catch(()=>new Response('<!DOCTYPE html><html><head><title>App</title></head><body><div id="q-app">Loading...</div></body></html>',{headers:{"Content-Type":"text/html"}}))))}})();
