import{k as Fe,r as p,b as ke,c as Ee,w as be,I as u,J as Q,L as m,a2 as W,M as i,a3 as de,P as l,O as g,az as r,aC as k,aA as C,A as _,aB as J,bf as T,be as h,N as V,a8 as ie,aD as he,a6 as Ae}from"./index.3dd97541.js";import{Q as R}from"./QSelect.1285d7d3.js";import{Q as Se,a as qe}from"./QItem.87fe5c76.js";import{Q as De}from"./QSpinnerDots.e03dba06.js";import{Q as Re}from"./QLinearProgress.fa832131.js";import{Q as Ve}from"./QPage.8036a276.js";import{u as Be,_ as Le}from"./IndexPage.cdd7610e.js";import{Q as Me,a as $e,b as Te,c as Qe,u as ze}from"./QTabPanels.7809bfe9.js";import{L as ye}from"./lotto.42d4db2a.js";import{p as A}from"./padding.dd505b59.js";import{u as xe}from"./useLotteryAnalysis.c47ac7e8.js";import{Q as Pe,a as Ce,b as D,c as Ue}from"./QTable.4ae3294d.js";import{_ as je}from"./plugin-vue_export-helper.21dcd24c.js";import"./position-engine.bb1fccc0.js";import"./selection.ebf7bbb8.js";import"./QPopupProxy.33948bdc.js";import"./QResizeObserver.f0438521.js";import"./touch.9135741d.js";import"./use-render-cache.3aae9b27.js";import"./QList.e8f9ac34.js";const He={class:"row q-gutter-y-md"},Oe={class:"col-12 col-sm-4 draw-title text-center"},Ne={class:"text-period"},Ie={class:"text-draw-date"},Ge={class:"col-12 col-sm-6 self-center"},We={class:"row justify-center"},Je={key:0,class:"col-auto"},Ze={class:"row q-my-md q-gutter-sm items-center"},Ke={class:"col-sm-auto"},Xe={class:"col-sm-auto"},Ye={class:"row q-my-md q-gutter-sm items-center"},et={class:"col-sm-auto"},tt={class:"col-sm-auto"},lt={class:"row q-col-gutter-xs"},st={class:"ball tail-number"},at={class:"row q-col-gutter-xs"},ot={class:"ball tail-number"},ut={class:"row q-col-gutter-xs"},nt={class:"ball tail-number"},rt={class:"row q-gutter-sm items-center q-mb-md"},it={class:"ball tail-number q-mx-xs"},dt={class:"row q-gutter-sm items-center q-mb-md"},ct={class:"ball tail-number q-mx-xs"},mt={class:"row q-gutter-sm items-center"},pt={class:"ball tail-number q-mx-xs"},vt={class:"text-subtitle1"},bt={class:"row justify-center"},ft={class:"row justify-center"},gt={class:"row justify-center"},_t={class:"row justify-center"},wt={class:"row justify-center"},yt={class:"row justify-center"},Ct={class:"row justify-center"},Ft={class:"row justify-center"},xt={class:"row justify-center"},kt={class:"row justify-center"},Et={class:"row no-wrap"},ht={key:0,class:"text-subtitle1 ball special-number"},At={class:"row no-wrap"},St=Fe({__name:"PatternResult",props:{isSuperLotto:{type:Boolean},drawResults:{},predictResult:{},rdResults:{},tailRdResults:{}},setup(fe){const F=fe,S=p("1"),q=xe();let E=p(new Map);const te=[{name:"draw_date",label:"\u65E5\u671F",field:"draw_date",align:"center"},{name:"tail1",label:"\u5C3E1",field:"tail1",align:"center"},{name:"tail2",label:"\u5C3E2",field:"tail2",align:"center"},{name:"tail3",label:"\u5C3E3",field:"tail3",align:"center"},{name:"tail4",label:"\u5C3E4",field:"tail4",align:"center"},{name:"tail5",label:"\u5C3E5",field:"tail5",align:"center"},{name:"tail6",label:"\u5C3E6",field:"tail6",align:"center"},{name:"tail7",label:"\u5C3E7",field:"tail7",align:"center"},{name:"tail8",label:"\u5C3E8",field:"tail8",align:"center"},{name:"tail9",label:"\u5C3E9",field:"tail9",align:"center"},{name:"tail0",label:"\u5C3E0",field:"tail0",align:"center"},{name:"draw_results",label:"\u734E\u865F",field:"draw_results",align:"center"},{name:"tail_set",label:"\u5C3E\u6578",field:"tail_set",align:"center"}];ke(()=>{Z()});const z=p(new Map),B=p(new Map),Z=()=>{var s;E.value.clear(),G.value=[],H.value=[],y.value=[],z.value.clear();for(const t of F.drawResults){t.tails=new Map;for(let a=0;a<10;a++)t.tails.set(a,[]);let e=[...t.draw_number_size];!F.isSuperLotto&&t.special_number&&(e.push(t.special_number),e=e.sort((a,o)=>a-o));for(const a of e){const o=a%10;(s=t.tails.get(o))==null||s.push(a)}t.tailSet=q.getTailSet(t,F.isSuperLotto);for(const a of t.tailSet)z.value.set(a,(z.value.get(a)||0)+1)}z.value=I(z.value);for(const t of Array(10).keys())E.value.set(t,0);ue(),X(),me(),ve(),E.value=I(E.value),n()},L=p(1),le=p([{label:"\u5DF2\u9023\u7E8C\u62D6\u51FA 1 \u6B21",value:1},{label:"\u5DF2\u9023\u7E8C\u62D6\u51FA 2 \u6B21",value:2},{label:"\u5DF2\u9023\u7E8C\u62D6\u51FA 3 \u6B21",value:3},{label:"\u5DF2\u9023\u7E8C\u62D6\u51FA 4 \u6B21",value:4},{label:"\u5DF2\u9023\u7E8C\u62D6\u51FA 5 \u6B21",value:5}]),M=p(1),P=p("above"),O=p([{label:"(\u542B)\u4EE5\u4E0A",value:"above"},{label:"(\u542B)\u4EE5\u4E0B",value:"below"},{label:"\u525B\u597D",value:"exact"}]),se=p(1),ce=Ee(()=>Array.from({length:se.value},(s,t)=>({label:`\u5DF2\u9023\u7E8C\u62D6\u51FA ${t+1} \u6B21`,value:t+1}))),K=p("above"),ae=p([{label:"(\u542B)\u4EE5\u4E0A",value:"above"},{label:"(\u542B)\u4EE5\u4E0B",value:"below"},{label:"\u525B\u597D",value:"exact"}]);be([L,P,M,K],()=>{Z()});const ue=()=>{const s=p([]);B.value.clear(),s.value=F.rdResults.filter(o=>{switch(P.value){case"above":return o.consecutiveHits>=L.value;case"below":return o.consecutiveHits<=L.value;case"exact":return o.consecutiveHits===L.value;default:return o.consecutiveHits>=L.value}});const t=new Map,e=ne(s.value);let a=0;for(const o of s.value){let v=new Set;for(const w of o.targetNumbers){const d=w%10,ee=o.consecutiveHits,re=U(o,e.get(d)||0);t.set(d,(t.get(d)||0)+ee+re),a+=o.consecutiveHits,v.add(d)}for(const w of v)B.value.set(w,(B.value.get(w)||0)+1)}B.value=I(B.value);for(const o of t.keys()){const v=t.get(o)||0;Y(o,v/a)}},$=p(new Map),X=()=>{const s=p([]);$.value.clear(),s.value=F.tailRdResults.filter(a=>{switch(K.value){case"above":return a.consecutiveHits>=M.value;case"below":return a.consecutiveHits<=M.value;case"exact":return a.consecutiveHits===M.value;default:return a.consecutiveHits>=M.value}}),se.value=1,se.value=Math.max(...s.value.map(a=>a.consecutiveHits));const t=ne(s.value);let e=0;for(const a of s.value)for(const o of a.targetNumbers){const v=a.consecutiveHits,w=U(a,t.get(o)||0);$.value.set(o,($.value.get(o)||0)+v+w),e+=a.consecutiveHits}$.value=I($.value);for(const a of $.value.keys()){const o=$.value.get(a)||0;Y(a,o/e)}},ne=s=>{const t=new Map,e=new Map;for(const a of s)for(const o of a.targetNumbers){const v=o%10;t.set(v,(t.get(v)||0)+o),e.set(v,(e.get(v)||0)+1)}for(const a of t.keys()){const o=e.get(a)||1;t.set(a,Number((t.get(a)||0)/o))}return t},U=(s,t)=>(s.consecutiveHits+5*t)/(s.consecutiveHits+5),me=()=>{const s=F.drawResults,t=s.slice(-10),e=oe(s),a=j(t),o=new Map;e.forEach((w,d)=>{const ee=a.get(d)||0;o.set(d,(w+ee)/2)});const v=N(o);for(const w of o.keys()){const d=v.get(w)||0;Y(w,d)}},oe=s=>{const t=new Map;s.forEach(o=>{if(o.draw_number_size.forEach(v=>{const w=v%10;t.set(w,(t.get(w)||0)+1)}),!F.isSuperLotto&&o.special_number){const v=o.special_number%10;t.set(v,(t.get(v)||0)+1)}});const e=Math.max(...t.values()),a=new Map;return t.forEach((o,v)=>a.set(v,o/e)),a},j=s=>{const t=new Map,e=new Map;for(let o=1;o<s.length;o++){const v=s[o-1].tailSet,w=s[o].tailSet;!v||!w||v.length===0||w.length===0||v.forEach(d=>{t.set(d,(t.get(d)||0)+1),w.includes(d)&&e.set(d,(e.get(d)||0)+1)})}const a=new Map;return t.forEach((o,v)=>{const w=e.get(v)||0;a.set(v,w/o)}),a},N=s=>{const t=Array.from(s.values()),e=Math.min(...t),a=Math.max(...t),o=new Map;return s.forEach((v,w)=>o.set(w,a===e?0:(v-e)/(a-e))),o},Y=(s,t)=>{E.value.set(s,(E.value.get(s)||0)+t)},I=s=>{const t=Array.from(s.entries());return t.sort((e,a)=>a[1]-e[1]),new Map(t)},pe=p(3),ve=()=>{for(const s of E.value.keys()){const t=E.value.get(s)||0;E.value.set(s,Number((t/pe.value*100).toFixed(1)))}},G=p([]),H=p([]),y=p([]),n=()=>{G.value=[],H.value=[],y.value=[];const s=Array.from(B.value.entries()).map(a=>a[0]).slice(0,5),t=Array.from($.value.entries()).map(a=>a[0]).slice(0,5),e=Array.from(E.value.entries()).map(a=>a[0]).slice(0,5);for(const a of s)t.includes(a)&&G.value.push(a);for(const a of s)e.includes(a)&&H.value.push(a);for(const a of t)e.includes(a)&&y.value.push(a);G.value.sort((a,o)=>a===0?1:o===0?-1:a-o),H.value.sort((a,o)=>a===0?1:o===0?-1:a-o),y.value.sort((a,o)=>a===0?1:o===0?-1:a-o)},x=s=>s&&Array.from(s).length===0?"#f8d7da":"",f=s=>{var t,e;return(e=(t=F.predictResult)==null?void 0:t.tailSet)!=null&&e.length?F.predictResult.tailSet.includes(s):!1};return(s,t)=>(u(),Q(W,{class:"q-mt-md"},{default:m(()=>[i(de,null,{default:m(()=>[i(Me,{modelValue:S.value,"onUpdate:modelValue":t[0]||(t[0]=e=>S.value=e),dense:"",align:"justify",class:"text-h6","active-color":"primary","indicator-color":"primary"},{default:m(()=>[i($e,{name:"1",label:"\u7248\u8DEF\u5206\u6790"})]),_:1},8,["modelValue"]),i(Te,{modelValue:S.value,"onUpdate:modelValue":t[5]||(t[5]=e=>S.value=e)},{default:m(()=>[i(Qe,{name:"1"},{default:m(()=>[s.predictResult.period?(u(),Q(W,{key:0,bordered:"",class:"ball-card full-width q-my-lg"},{default:m(()=>[i(de,null,{default:m(()=>[l("div",He,[l("div",Oe,[t[6]||(t[6]=l("div",{class:"text-h6"},"\u9810\u6E2C\u958B\u734E\u7D50\u679C",-1)),l("div",Ne," \u7B2C "+g(s.predictResult.period)+" \u671F ",1),l("div",Ie," \u958B\u734E\u65E5\u671F\uFF1A"+g(s.predictResult.draw_date),1)]),l("div",Ge,[l("div",We,[(u(!0),r(C,null,k(s.predictResult.draw_number_size,e=>(u(),r("div",{key:e,class:"col-auto"},[(u(),r("div",{class:"ball",key:e},g(_(A)(e)),1))]))),128)),s.predictResult.special_number?(u(),r("div",Je,[(u(),r("div",{class:"ball special-number",key:s.predictResult.special_number},g(_(A)(s.predictResult.special_number)),1))])):J("",!0)])])])]),_:1})]),_:1})):J("",!0),l("div",Ze,[t[7]||(t[7]=l("div",{class:"col-12 text-h6"},"\u7248\u8DEF\u5206\u6790\u7BE9\u9078",-1)),l("div",Ke,[i(R,{outlined:"",dense:"",modelValue:L.value,"onUpdate:modelValue":t[1]||(t[1]=e=>L.value=e),options:le.value,"map-options":"","emit-value":""},null,8,["modelValue","options"])]),l("div",Xe,[i(R,{outlined:"",dense:"",modelValue:P.value,"onUpdate:modelValue":t[2]||(t[2]=e=>P.value=e),options:O.value,"map-options":"","emit-value":""},null,8,["modelValue","options"])])]),l("div",Ye,[t[8]||(t[8]=l("div",{class:"col-12 text-h6"},"\u5C3E\u6578\u5206\u6790\u7BE9\u9078",-1)),l("div",et,[i(R,{outlined:"",dense:"",modelValue:M.value,"onUpdate:modelValue":t[3]||(t[3]=e=>M.value=e),options:ce.value,"map-options":"","emit-value":""},null,8,["modelValue","options"])]),l("div",tt,[i(R,{outlined:"",dense:"",modelValue:K.value,"onUpdate:modelValue":t[4]||(t[4]=e=>K.value=e),options:ae.value,"map-options":"","emit-value":""},null,8,["modelValue","options"])])]),t[22]||(t[22]=l("div",{class:"row q-mb-sm"},[l("label",{class:"col text-h6 text-bold"}," \u62D6\u724C\uFF1A\u5C3E\u6578\u9810\u6E2C\u958B\u51FA\u6A5F\u7387 (\u7531\u9AD8\u81F3\u4F4E\u6392\u5E8F) ")],-1)),i(W,{flat:"",bordered:"",class:"q-mb-lg q-pa-sm appearance"},{default:m(()=>[l("div",lt,[(u(!0),r(C,null,k(B.value.entries(),([e])=>(u(),r("div",{class:h(["col-4 col-md-2 text-h6",{predict:f(e)}]),key:e},[l("span",st,g(e),1)],2))),128))])]),_:1}),t[23]||(t[23]=l("div",{class:"row q-mb-sm"},[l("label",{class:"col text-h6 text-bold"}," \u5C3E\u6578\uFF1A\u9810\u6E2C\u958B\u51FA\u6A5F\u7387 (\u7531\u9AD8\u81F3\u4F4E\u6392\u5E8F) ")],-1)),i(W,{flat:"",bordered:"",class:"q-mb-lg q-pa-sm appearance"},{default:m(()=>[l("div",at,[(u(!0),r(C,null,k($.value.entries(),([e])=>(u(),r("div",{class:h(["col-4 col-md-2 text-h6",{predict:f(e)}]),key:e},[l("span",ot,g(e),1)],2))),128))])]),_:1}),t[24]||(t[24]=l("div",{class:"row q-mb-sm"},[l("label",{class:"col text-h6 text-bold"}," \u7248\u8DEF\uFF1A\u5C3E\u6578\u9810\u6E2C\u958B\u51FA\u6A5F\u7387\uFF08\u7531\u9AD8\u81F3\u4F4E\u6392\u5E8F\uFF09 ")],-1)),i(W,{flat:"",bordered:"",class:"q-mb-lg q-pa-sm appearance"},{default:m(()=>[l("div",ut,[(u(!0),r(C,null,k(_(E).entries(),([e])=>(u(),r("div",{class:h(["col-4 col-md-2 text-h6",{predict:f(e)}]),key:e},[l("span",nt,g(e),1)],2))),128))])]),_:1}),t[25]||(t[25]=l("div",{class:"row q-mb-sm"},[l("label",{class:"col text-h6 text-bold"}," \u7D9C\u5408\u6BD4\u5C0D\u9810\u6E2C\u5C3E\u6578 ")],-1)),i(W,{flat:"",bordered:"",class:"q-mb-lg q-pa-sm appearance"},{default:m(()=>[l("div",rt,[t[9]||(t[9]=l("span",{class:"text-h6"},"\u62D6\u724C+\u5C3E\u6578\uFF1A",-1)),(u(!0),r(C,null,k(G.value.entries(),([,e])=>(u(),r("span",{key:e,class:h({predict:f(e)})},[l("span",it,g(e),1)],2))),128))]),l("div",dt,[t[10]||(t[10]=l("span",{class:"text-h6"},"\u62D6\u724C+\u7248\u8DEF\uFF1A",-1)),(u(!0),r(C,null,k(H.value.entries(),([,e])=>(u(),r("span",{key:e,class:h({predict:f(e)})},[l("span",ct,g(e),1)],2))),128))]),l("div",mt,[t[11]||(t[11]=l("span",{class:"text-h6"},"\u5C3E\u6578+\u7248\u8DEF\uFF1A",-1)),(u(!0),r(C,null,k(y.value.entries(),([,e])=>(u(),r("span",{key:e,class:h({predict:f(e)})},[l("span",pt,g(e),1)],2))),128))])]),_:1}),i(Pe,{rows:s.drawResults,columns:te,"rows-per-page-options":[0],"hide-bottom":"","hide-pagination":"","virtual-scroll":"",separator:"cell",class:"sticky-virtscroll-table q-mt-lg"},{header:m(e=>[i(Ce,{props:e,class:"bg-primary text-white"},{default:m(()=>[(u(!0),r(C,null,k(e.cols,a=>(u(),Q(Ue,{key:a.name,props:e},{default:m(()=>[V(g(a.label),1)]),_:2},1032,["props"]))),128))]),_:2},1032,["props"])]),body:m(e=>[i(Ce,{props:e},{default:m(()=>{var a,o,v,w,d,ee,re,ge,_e,we;return[i(D,{key:"draw_date",props:e},{default:m(()=>[l("div",vt,g(e.row.draw_date),1)]),_:2},1032,["props"]),i(D,{key:"tail1",props:e,class:"fixed-col",style:T({backgroundColor:x((a=e.row.tails)==null?void 0:a.get(1))})},{default:m(()=>{var b;return[l("div",bt,[(u(!0),r(C,null,k((b=e.row.tails)==null?void 0:b.get(1),c=>(u(),r("span",{key:c,class:h(["text-h6 ball col-6",{"special-number":c===e.row.special_number&&!s.isSuperLotto}])},[V(g(_(A)(c))+" ",1),t[12]||(t[12]=l("br",null,null,-1))],2))),128))])]}),_:2},1032,["props","style"]),i(D,{key:"tail2",props:e,class:"fixed-col",style:T({backgroundColor:x((o=e.row.tails)==null?void 0:o.get(2))})},{default:m(()=>{var b;return[l("div",ft,[(u(!0),r(C,null,k((b=e.row.tails)==null?void 0:b.get(2),c=>(u(),r("span",{key:c,class:h(["text-h6 ball col-6",{"special-number":c===e.row.special_number&&!s.isSuperLotto}])},[V(g(_(A)(c)),1),t[13]||(t[13]=l("br",null,null,-1))],2))),128))])]}),_:2},1032,["props","style"]),i(D,{key:"tail3",props:e,class:"fixed-col",style:T({backgroundColor:x((v=e.row.tails)==null?void 0:v.get(3))})},{default:m(()=>{var b;return[l("div",gt,[(u(!0),r(C,null,k((b=e.row.tails)==null?void 0:b.get(3),c=>(u(),r("span",{key:c,class:h(["text-h6 ball col-6",{"special-number":c===e.row.special_number&&!s.isSuperLotto}])},[V(g(_(A)(c)),1),t[14]||(t[14]=l("br",null,null,-1))],2))),128))])]}),_:2},1032,["props","style"]),i(D,{key:"tail4",props:e,class:"fixed-col",style:T({backgroundColor:x((w=e.row.tails)==null?void 0:w.get(4))})},{default:m(()=>{var b;return[l("div",_t,[(u(!0),r(C,null,k((b=e.row.tails)==null?void 0:b.get(4),c=>(u(),r("span",{key:c,class:h(["text-h6 ball col-6",{"special-number":c===e.row.special_number&&!s.isSuperLotto}])},[V(g(_(A)(c)),1),t[15]||(t[15]=l("br",null,null,-1))],2))),128))])]}),_:2},1032,["props","style"]),i(D,{key:"tail5",props:e,class:"fixed-col",style:T({backgroundColor:x((d=e.row.tails)==null?void 0:d.get(5))})},{default:m(()=>{var b;return[l("div",wt,[(u(!0),r(C,null,k((b=e.row.tails)==null?void 0:b.get(5),c=>(u(),r("span",{key:c,class:h(["text-h6 ball col-6",{"special-number":c===e.row.special_number&&!s.isSuperLotto}])},[V(g(_(A)(c)),1),t[16]||(t[16]=l("br",null,null,-1))],2))),128))])]}),_:2},1032,["props","style"]),i(D,{key:"tail6",props:e,class:"fixed-col",style:T({backgroundColor:x((ee=e.row.tails)==null?void 0:ee.get(6))})},{default:m(()=>{var b;return[l("div",yt,[(u(!0),r(C,null,k((b=e.row.tails)==null?void 0:b.get(6),c=>(u(),r("span",{key:c,class:h(["text-h6 ball col-6",{"special-number":c===e.row.special_number&&!s.isSuperLotto}])},[V(g(_(A)(c)),1),t[17]||(t[17]=l("br",null,null,-1))],2))),128))])]}),_:2},1032,["props","style"]),i(D,{key:"tail7",props:e,class:"fixed-col",style:T({backgroundColor:x((re=e.row.tails)==null?void 0:re.get(7))})},{default:m(()=>{var b;return[l("div",Ct,[(u(!0),r(C,null,k((b=e.row.tails)==null?void 0:b.get(7),c=>(u(),r("span",{key:c,class:h(["text-h6 ball col-6",{"special-number":c===e.row.special_number&&!s.isSuperLotto}])},[V(g(_(A)(c)),1),t[18]||(t[18]=l("br",null,null,-1))],2))),128))])]}),_:2},1032,["props","style"]),i(D,{key:"tail8",props:e,class:"fixed-col",style:T({backgroundColor:x((ge=e.row.tails)==null?void 0:ge.get(8))})},{default:m(()=>{var b;return[l("div",Ft,[(u(!0),r(C,null,k((b=e.row.tails)==null?void 0:b.get(8),c=>(u(),r("span",{key:c,class:h(["text-h6 ball col-6",{"special-number":c===e.row.special_number&&!s.isSuperLotto}])},[V(g(_(A)(c)),1),t[19]||(t[19]=l("br",null,null,-1))],2))),128))])]}),_:2},1032,["props","style"]),i(D,{key:"tail9",props:e,class:"fixed-col",style:T({backgroundColor:x((_e=e.row.tails)==null?void 0:_e.get(9))})},{default:m(()=>{var b;return[l("div",xt,[(u(!0),r(C,null,k((b=e.row.tails)==null?void 0:b.get(9),c=>(u(),r("span",{key:c,class:h(["text-h6 ball col-6",{"special-number":c===e.row.special_number&&!s.isSuperLotto}])},[V(g(_(A)(c)),1),t[20]||(t[20]=l("br",null,null,-1))],2))),128))])]}),_:2},1032,["props","style"]),i(D,{key:"tail0",props:e,class:"fixed-col",style:T({backgroundColor:x((we=e.row.tails)==null?void 0:we.get(0))})},{default:m(()=>{var b;return[l("div",kt,[(u(!0),r(C,null,k((b=e.row.tails)==null?void 0:b.get(0),c=>(u(),r("span",{key:c,class:h(["text-h6 ball col-6",{"special-number":c===e.row.special_number&&!s.isSuperLotto}])},[V(g(_(A)(c)),1),t[21]||(t[21]=l("br",null,null,-1))],2))),128))])]}),_:2},1032,["props","style"]),i(D,{key:"draw_results",props:e},{default:m(()=>[l("div",Et,[(u(!0),r(C,null,k(e.row.draw_number_size,b=>(u(),r("span",{key:b,class:"text-subtitle1 ball"},g(_(A)(b)),1))),128)),e.row.special_number?(u(),r("span",ht,g(_(A)(e.row.special_number)),1)):J("",!0)])]),_:2},1032,["props"]),i(D,{key:"tail_set",props:e},{default:m(()=>[l("div",At,[(u(!0),r(C,null,k(e.row.tailSet,b=>(u(),r("span",{key:b,class:"text-subtitle1 ball tail-number"},g(b),1))),128))])]),_:2},1032,["props"])]}),_:2},1032,["props"])]),_:1},8,["rows"])]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1}))}});var qt=je(St,[["__scopeId","data-v-0ad013a4"]]);const Dt={class:"row lto-ref q-mb-sm"},Rt={class:"col-12 col-sm-4 self-center text-h6"},Vt={class:"col-12 col-sm-6 self-center text-subtitle1"},Bt={class:"row balls"},Lt={class:"ball"},Mt={key:0,class:"col-auto"},$t={class:"row q-mb-md"},Tt={class:"col"},Qt={key:1,class:"row q-mb-md"},zt={class:"row q-mb-md"},Pt={class:"col-12 col-sm-4 q-pa-sm"},Ut={class:"col-12 col-sm-4 q-pa-sm"},jt={class:"col-12 col-sm-4 q-pa-sm"},Ht={class:"row q-mb-md"},Ot={class:"col-12 col-sm-4 q-pa-sm"},Nt={class:"col-12 col-sm-4 q-pa-sm"},It={class:"col-12 col-sm-4 q-pa-sm"},Gt={class:"row q-mb-md"},Wt={class:"col-12 col-sm-4"},Jt={class:"q-pa-sm"},Zt={class:"col-12 col-sm-4"},Kt={class:"q-pa-sm"},Xt={class:"col-12 col-sm-4"},Yt={class:"q-pa-sm"},el={class:"text-center q-mb-sm"},Cl=Fe({__name:"PatternPage",setup(fe){const F=Be(),S=p(F.getLotto),q=ze(),E=xe(),te=p(1),z=p(1),B=p(1),Z=p(1),L=p(1),le=p(1),M=[{label:"\u4E00\u661F\u7D44\u5408",value:1},{label:"\u4E8C\u661F\u7D44\u5408",value:2},{label:"\u4E09\u661F\u7D44\u5408",value:3}],P=[{label:"\u4E00\u661F\u7D44\u5408",value:1},{label:"\u4E8C\u661F\u7D44\u5408",value:2},{label:"\u4E09\u661F\u7D44\u5408",value:3},{label:"\u56DB\u661F\u7D44\u5408",value:4},{label:"\u4E94\u661F\u7D44\u5408",value:5}],O=p(!1),se=()=>{O.value=!0},ce=()=>{O.value=!1},K=()=>{O.value=!1};be(()=>F.getLotto,y=>{y&&(S.value=y)}),be(()=>{var y;return(y=S.value)==null?void 0:y.period},()=>{j.value=[]});const ae=p(50);let ue=p(Array.from({length:991},(y,n)=>({label:`${n+10}\u671F`,value:n+10})));const $=(y,n,x)=>{const f=parseInt(y,10);(f<10||f>1e3)&&x(),n(()=>{ue.value=Array.from({length:991},(s,t)=>t+10).filter(s=>s.toString().startsWith(y)).map(s=>({label:`${s.toString()}\u671F`,value:s}))})},X=p(20),ne=Array.from({length:21},(y,n)=>({label:`${n+10}\u671F`,value:n+10})),U=p(1),me=Array.from({length:15},(y,n)=>({label:`\u4E0B${n+1}\u671F`,value:n+1})),oe=p(!1),j=p([]),N=p({period:"",draw_date:"",draw_number_appear:[],draw_number_size:[],tails:new Map}),Y=p([]),I=p([]),pe=async()=>{var y,n,x;try{q.startCalculating(),oe.value=F.isSuperLotto;const f=await ye.getLottoList({draw_type:F.getDrawType,date_end:(n=(y=S.value)==null?void 0:y.draw_date)!=null?n:"",limit:ae.value});j.value=f.data.reverse();const s=await ye.getLottoPredict({draw_type:F.getDrawType,draw_date:(x=F.getLotto)==null?void 0:x.draw_date,ahead_count:U.value});N.value=s.data,N.value.period&&(N.value.tailSet=E.getTailSet(N.value,oe.value));const t=await ve();Y.value=t.data;const e=await G();I.value=e.data}catch(f){console.error("\u8A08\u7B97\u932F\u8AA4:",f)}finally{H()}},ve=()=>{const y=j.value.map(f=>{const s=[...f.draw_number_size];return f.special_number&&!F.isSuperLotto&&s.push(f.special_number),{numbers:[...s],period:String(f.period)}});E.setResults(y),E.setConfig({firstGroupSize:te.value,secondGroupSize:z.value,targetGroupSize:B.value,maxRange:X.value,lookAheadCount:U.value});let n=Date.now();const x=8;return E.analyzeWithProgress(async f=>{const s=Date.now();s-n>=x&&(await q.updateProgress(f),n=s)},f=>{q.addWarning(f)})},G=()=>{const y=j.value.map(f=>{const s=new Set;for(let e of f.draw_number_size)s.add(e%10);!F.isSuperLotto&&f.special_number&&s.add(f.special_number%10);const t=Array.from(s).sort((e,a)=>e===0?1:a===0?-1:e-a);return{period:String(f.period),numbers:[...t]}});E.init({firstGroupSize:Z.value,secondGroupSize:L.value,targetGroupSize:le.value,maxRange:X.value,lookAheadCount:U.value},y);let n=Date.now();const x=8;return E.analyzeWithProgress(async f=>{const s=Date.now();s-n>=x&&(await q.updateProgress(f),n=s)},f=>{q.addWarning(f)})},H=()=>{q.stopCalculating(),E.stopAnalyzer()};return(y,n)=>(u(),Q(Ve,{class:"justify-center"},{default:m(()=>[i(W,null,{default:m(()=>[i(de,null,{default:m(()=>{var x,f,s,t,e,a,o,v,w;return[(x=_(F).getLotto)!=null&&x.draw_date?(u(),r(C,{key:0},[l("div",Dt,[l("div",Rt,[l("div",null,g(_(F).getDrawLabel),1),n[9]||(n[9]=l("span",null,"\u53C3\u8003\u671F\u865F\uFF1A",-1)),l("span",null,g((f=S.value)==null?void 0:f.period),1),l("span",null,"\uFF08"+g((s=S.value)==null?void 0:s.draw_date)+"\uFF09",1)]),l("div",Vt,[l("div",Bt,[(u(!0),r(C,null,k((t=S.value)==null?void 0:t.draw_number_size,d=>(u(),r("div",{class:"col-auto",key:d},[l("div",Lt,g(_(A)(d)),1)]))),128)),(e=S.value)!=null&&e.special_number?(u(),r("div",Mt,[(u(),r("div",{class:"ball special-number",key:(a=S.value)==null?void 0:a.special_number},g(_(A)((o=S.value)==null?void 0:o.special_number)),1))])):J("",!0)])])]),l("div",$t,[l("div",Tt,[O.value?(u(),Q(ie,{key:1,type:"button",label:"\u53D6\u6D88\u9078\u64C7",color:"negative",class:"text-h6 q-ml-md",onClick:K})):(u(),Q(ie,{key:0,type:"button",label:"\u91CD\u65B0\u9078\u64C7",color:"primary",class:"text-h6 q-ml-md",onClick:se}))])])],64)):(u(),r("div",Qt,n[10]||(n[10]=[l("div",{class:"text-h6"},"\u203B\u8ACB\u9078\u64C7\u53C3\u8003\u671F\u865F",-1)]))),i(he,{class:"q-mb-md"}),!O.value&&((v=_(F).getLotto)==null?void 0:v.draw_date)?(u(),r(C,{key:2},[n[17]||(n[17]=l("div",{class:"row q-mb-md"},[l("div",{class:"col-12 text-h5 text-weight-bolder text-center"}," \u7D9C\u5408\u5206\u6790\u8A2D\u5B9A ")],-1)),l("div",zt,[n[11]||(n[11]=l("div",{class:"col-12 text-h6 text-weight-bold"},"\u734E\u865F\u62D6\u724C\u7D44\u5408",-1)),l("div",Pt,[i(R,{outlined:"",dense:"",modelValue:te.value,"onUpdate:modelValue":n[0]||(n[0]=d=>te.value=d),options:M,"emit-value":"","map-options":""},null,8,["modelValue"])]),l("div",Ut,[i(R,{outlined:"",dense:"",modelValue:z.value,"onUpdate:modelValue":n[1]||(n[1]=d=>z.value=d),options:M,"emit-value":"","map-options":""},null,8,["modelValue"])]),l("div",jt,[i(R,{outlined:"",dense:"",modelValue:B.value,"onUpdate:modelValue":n[2]||(n[2]=d=>B.value=d),options:M,"emit-value":"","map-options":""},null,8,["modelValue"])])]),l("div",Ht,[n[12]||(n[12]=l("div",{class:"col-12 text-h6 text-weight-bold"},"\u5C3E\u6578\u62D6\u724C\u7D44\u5408",-1)),l("div",Ot,[i(R,{outlined:"",dense:"",modelValue:Z.value,"onUpdate:modelValue":n[3]||(n[3]=d=>Z.value=d),options:P,"emit-value":"","map-options":""},null,8,["modelValue"])]),l("div",Nt,[i(R,{outlined:"",dense:"",modelValue:L.value,"onUpdate:modelValue":n[4]||(n[4]=d=>L.value=d),options:P,"emit-value":"","map-options":""},null,8,["modelValue"])]),l("div",It,[i(R,{outlined:"",dense:"",modelValue:le.value,"onUpdate:modelValue":n[5]||(n[5]=d=>le.value=d),options:P,"emit-value":"","map-options":""},null,8,["modelValue"])])]),l("div",Gt,[l("div",Wt,[n[14]||(n[14]=l("div",{class:"text-h6 text-weight-bold"},"\u63A8\u7B97\u671F\u6578",-1)),l("div",Jt,[i(R,{outlined:"",dense:"",modelValue:ae.value,"onUpdate:modelValue":n[6]||(n[6]=d=>ae.value=d),options:_(ue),"input-debounce":"0","use-input":"","hide-selected":"","fill-input":"",onFilter:$,"emit-value":"","map-options":""},{"no-option":m(()=>[i(Se,null,{default:m(()=>[i(qe,{class:"text-grey"},{default:m(()=>n[13]||(n[13]=[V(" \u7121\u53EF\u7528\u9078\u9805 ")])),_:1})]),_:1})]),_:1},8,["modelValue","options"])])]),l("div",Zt,[n[15]||(n[15]=l("div",{class:"text-h6 text-weight-bold"},"\u6700\u5927\u5340\u9593",-1)),l("div",Kt,[i(R,{outlined:"",dense:"",modelValue:X.value,"onUpdate:modelValue":n[7]||(n[7]=d=>X.value=d),options:_(ne),"emit-value":"","map-options":""},null,8,["modelValue","options"])])]),l("div",Xt,[n[16]||(n[16]=l("div",{class:"text-h6 text-weight-bold"},"\u9810\u6E2C\u671F\u6578",-1)),l("div",Yt,[i(R,{outlined:"",dense:"",modelValue:U.value,"onUpdate:modelValue":n[8]||(n[8]=d=>U.value=d),options:_(me),"emit-value":"","map-options":""},null,8,["modelValue","options"])])])]),i(Ae,{align:"right",class:"q-my-lg q-py-none q-px-md"},{default:m(()=>[_(q).isCalculating?(u(),Q(ie,{key:0,type:"button",label:"\u4E2D\u65B7\u8A08\u7B97",color:"negative",class:"text-h6 q-mr-md",onClick:H})):J("",!0),i(ie,{type:"button",label:"\u958B\u59CB\u8A08\u7B97",color:"positive",class:"text-h6 q-px-lg q-py-sm",onClick:pe,loading:_(q).isCalculating},{loading:m(()=>[i(De)]),_:1},8,["loading"])]),_:1})],64)):(u(),Q(Le,{key:3,"draw-type-query":_(F).drawType,"date-query":((w=S.value)==null?void 0:w.draw_date)||"","is-select-ref":!0,onSelectRef:ce},null,8,["draw-type-query","date-query"]))]}),_:1}),_(q).isCalculating?(u(),Q(de,{key:0},{default:m(()=>[l("div",el,g(_(q).progressMessage),1),i(Re,{rounded:"",size:"md",value:_(q).progress,"animation-speed":50,color:"primary",class:"q-mb-xs"},null,8,["value"])]),_:1})):J("",!0)]),_:1}),!_(q).isCalculating&&j.value.length>0?(u(),Q(qt,{key:0,"is-super-lotto":oe.value,"draw-results":j.value,"predict-result":N.value,"rd-results":Y.value,"tail-rd-results":I.value},null,8,["is-super-lotto","draw-results","predict-result","rd-results","tail-rd-results"])):J("",!0)]),_:1}))}});export{Cl as default};
